{"type": "TORIEL", "name": "<PERSON><PERSON>", "checkText": "TORIEL - ATK 80 DEF 80\n* Knows best for you.", "hp": 440, "attack": 80, "defense": 80, "exp": 0, "gold": 0, "acts": ["Check", "Talk", "Spare"], "attacks": [{"name": "fire_magic_easy", "duration": 5.0, "difficulty": 2, "bullets": [{"type": "WHITE", "pattern": "fire_wave", "count": 5, "speed": 60.0, "damage": 5}]}, {"name": "fire_magic_medium", "duration": 6.0, "difficulty": 3, "bullets": [{"type": "WHITE", "pattern": "fire_spiral", "count": 8, "speed": 70.0, "damage": 6}]}, {"name": "fire_magic_hard", "duration": 7.0, "difficulty": 4, "bullets": [{"type": "WHITE", "pattern": "fire_barrage", "count": 12, "speed": 80.0, "damage": 7}]}, {"name": "fire_magic_mercy", "duration": 4.0, "difficulty": 1, "bullets": [{"type": "WHITE", "pattern": "fire_avoid_player", "count": 6, "speed": 40.0, "damage": 1}]}], "flavorTexts": ["* <PERSON><PERSON> prepares a magical attack.", "* <PERSON><PERSON> acts aloof.", "* <PERSON><PERSON> is humming to herself.", "* Smells like butterscotch-cinnamon pie.", "* <PERSON><PERSON> looks through you.", "* <PERSON><PERSON>'s attacks are getting weaker."], "spareConditions": ["talked_enough", "low_health_mercy"], "sprite": "toriel_overworld", "battleSprite": "toriel_battle", "spriteOffset": [0, 0], "canSpare": true, "canFlee": false, "isBoss": true, "actResponses": {"talk": {"text": "* You tried to talk to <PERSON><PERSON>.\n* ... but she avoids your gaze.", "effect": "increment_talk_counter"}, "spare": {"text": "* You spared <PERSON><PERSON>.\n* <PERSON><PERSON> doesn't seem to want to fight you.", "effect": "check_spare_condition"}}, "specialMechanics": {"talkCounter": 0, "talkThreshold": 11, "mercyMode": false, "attacksAvoidPlayer": false}}