#include "GameData.h"
#include <iostream>
#include <algorithm>

SaveData::SaveData() {
    Reset();
}

void SaveData::Reset() {
    playerName = "";
    level = 1;
    hp = 20;
    maxHP = 20;
    attack = 10;
    defense = 10;
    exp = 0;
    gold = 0;
    
    currentRoom = "ruins_entrance";
    playerX = 320.0f;
    playerY = 400.0f;
    
    items.clear();
    weapon = "Stick";
    armor = "Bandage";
    
    currentRoute = RouteType::NEUTRAL;
    killCount = 0;
    spareCount = 0;
    playtime = 0.0f;
    
    flags.resize(static_cast<size_t>(GameFlag::COUNT), false);
    funValue = 0;
}

GameData::GameData() {
}

GameData::~GameData() {
    Shutdown();
}

bool GameData::Initialize() {
    std::cout << "Initializing Game Data..." << std::endl;
    m_saveData.Reset();
    GenerateFunValue();
    std::cout << "Game Data initialized successfully!" << std::endl;
    return true;
}

void GameData::Shutdown() {
    std::cout << "Shutting down Game Data..." << std::endl;
}

void GameData::SetFlag(GameFlag flag, bool value) {
    size_t index = static_cast<size_t>(flag);
    if (index < m_saveData.flags.size()) {
        m_saveData.flags[index] = value;
    }
}

bool GameData::GetFlag(GameFlag flag) const {
    size_t index = static_cast<size_t>(flag);
    if (index < m_saveData.flags.size()) {
        return m_saveData.flags[index];
    }
    return false;
}

void GameData::ClearAllFlags() {
    std::fill(m_saveData.flags.begin(), m_saveData.flags.end(), false);
}

RouteType GameData::DetermineRoute() const {
    if (IsGenocideRoute()) {
        return RouteType::GENOCIDE;
    } else if (IsTruePacifistRoute()) {
        return RouteType::TRUE_PACIFIST;
    } else if (IsPacifistRoute()) {
        return RouteType::PACIFIST;
    }
    return RouteType::NEUTRAL;
}

bool GameData::IsGenocideRoute() const {
    return m_saveData.killCount >= GENOCIDE_KILL_THRESHOLD;
}

bool GameData::IsPacifistRoute() const {
    return m_saveData.killCount == 0;
}

bool GameData::IsTruePacifistRoute() const {
    return IsPacifistRoute() && 
           GetFlag(GameFlag::TORIEL_SPARED) &&
           GetFlag(GameFlag::PAPYRUS_SPARED) &&
           GetFlag(GameFlag::UNDYNE_BEFRIENDED) &&
           GetFlag(GameFlag::ALPHYS_DATE_COMPLETE) &&
           GetFlag(GameFlag::TRUE_LAB_COMPLETE);
}

void GameData::GenerateFunValue() {
    // Generate random fun value between 1-100
    m_saveData.funValue = (rand() % 100) + 1;
    std::cout << "Fun value generated: " << m_saveData.funValue << std::endl;
}

void GameData::AddKill() {
    m_saveData.killCount++;
}

void GameData::AddSpare() {
    m_saveData.spareCount++;
}

void GameData::GainEXP(int amount) {
    m_saveData.exp += amount;
    // TODO: Level up logic
}

void GameData::GainGold(int amount) {
    m_saveData.gold += amount;
}

void GameData::LoseGold(int amount) {
    m_saveData.gold = std::max(0, m_saveData.gold - amount);
}

void GameData::Heal(int amount) {
    m_saveData.hp = std::min(m_saveData.maxHP, m_saveData.hp + amount);
}

void GameData::TakeDamage(int amount) {
    m_saveData.hp = std::max(0, m_saveData.hp - amount);
}

bool GameData::AddItem(const std::string& itemName) {
    if (m_saveData.items.size() < MAX_INVENTORY_SIZE) {
        m_saveData.items.push_back(itemName);
        return true;
    }
    return false;
}

bool GameData::RemoveItem(const std::string& itemName) {
    auto it = std::find(m_saveData.items.begin(), m_saveData.items.end(), itemName);
    if (it != m_saveData.items.end()) {
        m_saveData.items.erase(it);
        return true;
    }
    return false;
}

bool GameData::HasItem(const std::string& itemName) const {
    return std::find(m_saveData.items.begin(), m_saveData.items.end(), itemName) != m_saveData.items.end();
}

int GameData::GetItemCount(const std::string& itemName) const {
    return std::count(m_saveData.items.begin(), m_saveData.items.end(), itemName);
}

void GameData::EquipWeapon(const std::string& weaponName) {
    m_saveData.weapon = weaponName;
}

void GameData::EquipArmor(const std::string& armorName) {
    m_saveData.armor = armorName;
}

int GameData::GetTotalAttack() const {
    // TODO: Calculate based on equipped weapon and level
    return m_saveData.attack;
}

int GameData::GetTotalDefense() const {
    // TODO: Calculate based on equipped armor and level
    return m_saveData.defense;
}

bool GameData::CanEncounterGaster() const {
    return m_saveData.funValue >= 66;
}

bool GameData::ShouldShowSpecialDialogue(const std::string& character) const {
    // TODO: Implement special dialogue conditions
    return false;
}

std::string GameData::GetSpecialDialogueKey(const std::string& character) const {
    // TODO: Implement special dialogue key logic
    return "";
}
