#pragma once

#include <memory>
#include <chrono>
#include "core/Renderer.h"
#include "core/InputManager.h"
#include "core/AudioManager.h"
#include "core/AssetManager.h"
#include "core/SaveManager.h"
#include "states/GameStateManager.h"
#include "game/GameData.h"

class Game {
public:
    Game();
    ~Game();
    
    bool Initialize();
    void Run();
    void Shutdown();
    
    // Getters for core systems
    Renderer* GetRenderer() const { return m_renderer.get(); }
    InputManager* GetInputManager() const { return m_inputManager.get(); }
    AudioManager* GetAudioManager() const { return m_audioManager.get(); }
    AssetManager* GetAssetManager() const { return m_assetManager.get(); }
    SaveManager* GetSaveManager() const { return m_saveManager.get(); }
    GameStateManager* GetStateManager() const { return m_stateManager.get(); }
    GameData* GetGameData() const { return m_gameData.get(); }
    
    // Game constants
    static constexpr int WINDOW_WIDTH = 640;
    static constexpr int WINDOW_HEIGHT = 480;
    static constexpr int TARGET_FPS = 30;
    static constexpr float DELTA_TIME = 1.0f / TARGET_FPS;
    
private:
    void Update(float deltaTime);
    void Render();
    
    // Core systems
    std::unique_ptr<Renderer> m_renderer;
    std::unique_ptr<InputManager> m_inputManager;
    std::unique_ptr<AudioManager> m_audioManager;
    std::unique_ptr<AssetManager> m_assetManager;
    std::unique_ptr<SaveManager> m_saveManager;
    std::unique_ptr<GameStateManager> m_stateManager;
    std::unique_ptr<GameData> m_gameData;
    
    // Game loop control
    bool m_running;
    std::chrono::high_resolution_clock::time_point m_lastFrameTime;
};
