{"type": "FROGGIT", "name": "<PERSON><PERSON>", "checkText": "FROGGIT - ATK 4 DEF 1\n* Life is difficult for this enemy.", "hp": 20, "attack": 4, "defense": 1, "exp": 3, "gold": 2, "acts": ["Check", "Compliment", "<PERSON><PERSON><PERSON><PERSON>"], "attacks": [{"name": "flies_pattern", "duration": 4.0, "difficulty": 1, "bullets": [{"type": "WHITE", "pattern": "flies", "count": 3, "speed": 50.0, "damage": 4}]}], "flavorTexts": ["* <PERSON><PERSON> attacks you!", "* <PERSON><PERSON> doesn't seem to know why it's here.", "* <PERSON><PERSON> hops to and fro.", "* The battlefield is filled with the smell of mustard seed."], "spareConditions": ["compliment_used", "low_health"], "sprite": "froggit_overworld", "battleSprite": "froggit_battle", "spriteOffset": [0, 0], "canSpare": true, "canFlee": true, "isBoss": false, "actResponses": {"compliment": {"text": "* You compliment <PERSON><PERSON>.\n* <PERSON><PERSON> doesn't understand what you said, but is flattered anyway.", "effect": "make_spareable"}, "threaten": {"text": "* You threaten <PERSON><PERSON>.\n* <PERSON><PERSON> doesn't understand what you said, but is scared anyway.", "effect": "make_spareable"}}}