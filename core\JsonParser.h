#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <variant>

// Simple JSON parser for our needs (no external libraries)
class JsonValue {
public:
    enum Type {
        NULL_TYPE,
        BOOL,
        NUMBER,
        STRING,
        ARRAY,
        OBJECT
    };
    
    using JsonObject = std::unordered_map<std::string, std::shared_ptr<JsonValue>>;
    using JsonArray = std::vector<std::shared_ptr<JsonValue>>;
    using ValueType = std::variant<std::nullptr_t, bool, double, std::string, JsonArray, JsonObject>;
    
    JsonValue() : m_type(NULL_TYPE), m_value(nullptr) {}
    JsonValue(bool value) : m_type(BOOL), m_value(value) {}
    JsonValue(double value) : m_type(NUMBER), m_value(value) {}
    JsonValue(const std::string& value) : m_type(STRING), m_value(value) {}
    JsonValue(const JsonArray& value) : m_type(ARRAY), m_value(value) {}
    JsonValue(const JsonObject& value) : m_type(OBJECT), m_value(value) {}
    
    Type GetType() const { return m_type; }
    
    bool AsBool() const;
    double AsNumber() const;
    int AsInt() const { return static_cast<int>(AsNumber()); }
    float AsFloat() const { return static_cast<float>(AsNumber()); }
    std::string AsString() const;
    const JsonArray& AsArray() const;
    const JsonObject& AsObject() const;
    
    // Object access
    std::shared_ptr<JsonValue> operator[](const std::string& key) const;
    bool HasKey(const std::string& key) const;
    
    // Array access
    std::shared_ptr<JsonValue> operator[](size_t index) const;
    size_t Size() const;
    
    // Utility functions
    bool IsNull() const { return m_type == NULL_TYPE; }
    bool IsBool() const { return m_type == BOOL; }
    bool IsNumber() const { return m_type == NUMBER; }
    bool IsString() const { return m_type == STRING; }
    bool IsArray() const { return m_type == ARRAY; }
    bool IsObject() const { return m_type == OBJECT; }
    
private:
    Type m_type;
    ValueType m_value;
};

class JsonParser {
public:
    static std::shared_ptr<JsonValue> Parse(const std::string& json);
    static std::shared_ptr<JsonValue> ParseFromFile(const std::string& filename);
    static std::string Stringify(const JsonValue& value, int indent = 0);
    static bool SaveToFile(const JsonValue& value, const std::string& filename);
    
private:
    static std::shared_ptr<JsonValue> ParseValue(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseObject(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseArray(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseString(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseNumber(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseBool(const std::string& json, size_t& pos);
    static std::shared_ptr<JsonValue> ParseNull(const std::string& json, size_t& pos);
    
    static void SkipWhitespace(const std::string& json, size_t& pos);
    static std::string UnescapeString(const std::string& str);
    static std::string EscapeString(const std::string& str);
};

// Helper class for loading game data from JSON
class JsonDataLoader {
public:
    static bool LoadDialogueData(const std::string& filename, std::unordered_map<std::string, class DialogueData>& dialogues);
    static bool LoadMonsterData(const std::string& filename, std::unordered_map<std::string, class MonsterData>& monsters);
    static bool LoadItemData(const std::string& filename, std::unordered_map<std::string, class ItemData>& items);
    static bool LoadRoomData(const std::string& filename, std::unordered_map<std::string, class RoomData>& rooms);
    
private:
    static class DialogueData ParseDialogue(const JsonValue& json);
    static class MonsterData ParseMonster(const JsonValue& json);
    static class ItemData ParseItem(const JsonValue& json);
    static class RoomData ParseRoom(const JsonValue& json);
};
