#include "Game.h"
#include <iostream>
#include <thread>

Game::Game() : m_running(false) {
}

Game::~Game() {
    Shutdown();
}

bool Game::Initialize() {
    std::cout << "Initializing Undertale Recreation..." << std::endl;
    
    // Initialize core systems in order
    m_renderer = std::make_unique<Renderer>();
    if (!m_renderer->Initialize(WINDOW_WIDTH, WINDOW_HEIGHT)) {
        std::cerr << "Failed to initialize renderer!" << std::endl;
        return false;
    }
    
    m_inputManager = std::make_unique<InputManager>();
    if (!m_inputManager->Initialize()) {
        std::cerr << "Failed to initialize input manager!" << std::endl;
        return false;
    }
    
    m_audioManager = std::make_unique<AudioManager>();
    if (!m_audioManager->Initialize()) {
        std::cerr << "Failed to initialize audio manager!" << std::endl;
        return false;
    }
    
    m_assetManager = std::make_unique<AssetManager>();
    if (!m_assetManager->Initialize()) {
        std::cerr << "Failed to initialize asset manager!" << std::endl;
        return false;
    }
    
    m_saveManager = std::make_unique<SaveManager>();
    if (!m_saveManager->Initialize()) {
        std::cerr << "Failed to initialize save manager!" << std::endl;
        return false;
    }
    
    m_gameData = std::make_unique<GameData>();
    if (!m_gameData->Initialize()) {
        std::cerr << "Failed to initialize game data!" << std::endl;
        return false;
    }
    
    m_stateManager = std::make_unique<GameStateManager>();
    if (!m_stateManager->Initialize(this)) {
        std::cerr << "Failed to initialize state manager!" << std::endl;
        return false;
    }
    
    std::cout << "Game initialized successfully!" << std::endl;
    return true;
}

void Game::Run() {
    m_running = true;
    m_lastFrameTime = std::chrono::high_resolution_clock::now();
    
    std::cout << "Starting game loop..." << std::endl;
    
    while (m_running) {
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto deltaTime = std::chrono::duration<float>(currentTime - m_lastFrameTime).count();
        m_lastFrameTime = currentTime;
        
        // Cap delta time to prevent spiral of death
        if (deltaTime > 0.1f) {
            deltaTime = DELTA_TIME;
        }
        
        Update(deltaTime);
        Render();
        
        // Check if we should exit
        if (m_inputManager->ShouldQuit()) {
            m_running = false;
        }
        
        // Frame rate limiting
        std::this_thread::sleep_for(std::chrono::milliseconds(33)); // ~30 FPS
    }
    
    std::cout << "Game loop ended." << std::endl;
}

void Game::Update(float deltaTime) {
    m_inputManager->Update();
    m_stateManager->Update(deltaTime);
    m_audioManager->Update();
}

void Game::Render() {
    m_renderer->Clear();
    m_stateManager->Render(m_renderer.get());
    m_renderer->Present();
}

void Game::Shutdown() {
    std::cout << "Shutting down game..." << std::endl;
    
    // Shutdown in reverse order
    m_stateManager.reset();
    m_gameData.reset();
    m_saveManager.reset();
    m_assetManager.reset();
    m_audioManager.reset();
    m_inputManager.reset();
    m_renderer.reset();
    
    std::cout << "Game shutdown complete." << std::endl;
}
