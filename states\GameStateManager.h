#pragma once

#include <memory>
#include <stack>
#include <unordered_map>
#include <string>

// Forward declarations
class GameState;
class Game;
class Renderer;

enum class StateType {
    INTRO,
    MAIN_MENU,
    NAMING,
    OVERWORLD,
    BATTLE,
    D<PERSON><PERSON><PERSON>GUE,
    MENU,
    SAVE_LOAD,
    SETTINGS,
    GAME_OVER,
    CREDITS,
    TRUE_LAB,
    FLOWEY_FIGHT,
    ASRIEL_FIGHT
};

class GameStateManager {
public:
    GameStateManager();
    ~GameStateManager();
    
    bool Initialize(Game* game);
    void Update(float deltaTime);
    void Render(Renderer* renderer);
    void Shutdown();
    
    // State management
    void PushState(StateType type);
    void PopState();
    void ChangeState(StateType type);
    void ClearStates();
    
    // State queries
    GameState* GetCurrentState() const;
    StateType GetCurrentStateType() const;
    bool HasState(StateType type) const;
    int GetStateCount() const { return static_cast<int>(m_stateStack.size()); }
    
    // Game reference
    Game* GetGame() const { return m_game; }
    
private:
    std::unique_ptr<GameState> CreateState(StateType type);
    
    Game* m_game;
    std::stack<std::unique_ptr<GameState>> m_stateStack;
    std::unordered_map<StateType, std::unique_ptr<GameState>> m_stateCache;
    
    bool m_initialized;
};
