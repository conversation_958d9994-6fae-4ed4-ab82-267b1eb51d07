#pragma once

#include <string>
#include <unordered_map>
#include <memory>
#include <vector>

// Forward declarations
class AudioClip;
class MusicTrack;

struct AudioSource {
    std::shared_ptr<AudioClip> clip;
    float volume;
    float pitch;
    bool looping;
    bool playing;
    float position;
    int channel;
    
    AudioSource() : volume(1.0f), pitch(1.0f), looping(false), playing(false), position(0.0f), channel(-1) {}
};

class AudioManager {
public:
    AudioManager();
    ~AudioManager();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // Sound effects
    void PlaySound(const std::string& soundName, float volume = 1.0f, float pitch = 1.0f);
    void StopSound(const std::string& soundName);
    void StopAllSounds();
    
    // Music
    void PlayMusic(const std::string& musicName, bool loop = true, float volume = 1.0f);
    void StopMusic();
    void PauseMusic();
    void ResumeMusic();
    void SetMusicVolume(float volume);
    void FadeInMusic(const std::string& musicName, float fadeTime, bool loop = true);
    void FadeOutMusic(float fadeTime);
    
    // Master volume controls
    void SetMasterVolume(float volume);
    void SetSoundVolume(float volume);
    void SetMusicVolume(float volume);
    
    float GetMasterVolume() const { return m_masterVolume; }
    float GetSoundVolume() const { return m_soundVolume; }
    float GetMusicVolumeLevel() const { return m_musicVolume; }
    
    // Audio loading
    bool LoadSound(const std::string& name, const std::string& filename);
    bool LoadMusic(const std::string& name, const std::string& filename);
    
    // Undertale-specific audio functions
    void PlayMenuSound();
    void PlaySelectSound();
    void PlayCancelSound();
    void PlayDamageSound();
    void PlayHealSound();
    void PlaySaveSound();
    
private:
    void ProcessAudio();
    void MixAudio(std::vector<float>& buffer);
    
    // Audio data
    std::unordered_map<std::string, std::shared_ptr<AudioClip>> m_sounds;
    std::unordered_map<std::string, std::shared_ptr<MusicTrack>> m_music;
    
    // Active audio sources
    std::vector<AudioSource> m_activeSources;
    
    // Current music
    std::shared_ptr<MusicTrack> m_currentMusic;
    float m_musicPosition;
    bool m_musicPlaying;
    bool m_musicPaused;
    bool m_musicLooping;
    
    // Volume controls
    float m_masterVolume;
    float m_soundVolume;
    float m_musicVolume;
    
    // Fading
    bool m_fadingIn;
    bool m_fadingOut;
    float m_fadeTime;
    float m_fadeTimer;
    std::string m_nextMusic;
    
    // Audio system
    static constexpr int SAMPLE_RATE = 44100;
    static constexpr int CHANNELS = 2;
    static constexpr int BUFFER_SIZE = 1024;
    
    bool m_initialized;
};
