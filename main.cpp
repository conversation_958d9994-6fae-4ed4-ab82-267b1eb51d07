#include "Game.h"
#include "game/UndertaleData.h"
#include <iostream>
#include <cstdlib>
#include <ctime>
#include <thread>
#include <chrono>

int main() {
    // Seed random number generator
    srand(static_cast<unsigned int>(time(nullptr)));
    
    std::cout << "=== Undertale Recreation ===" << std::endl;
    std::cout << "Starting game..." << std::endl;
    
    Game game;
    
    if (!game.Initialize()) {
        std::cerr << "Failed to initialize game!" << std::endl;
        return -1;
    }
    
    // Load assets and test JSON system
    auto assetManager = game.GetAssetManager();
    if (assetManager) {
        std::cout << "\nLoading Undertale assets..." << std::endl;
        assetManager->LoadUndertaleAssets();
        assetManager->ListLoadedAssets();
        assetManager->ValidateAssets();
    }
    
    // Test JSON data loading
    std::cout << "\nTesting JSON data system..." << std::endl;
    if (assetManager) {
        auto torielDialogue = assetManager->GetDialogue("toriel_intro");
        if (torielDialogue) {
            std::cout << "Successfully loaded Toriel dialogue with " << torielDialogue->entries.size() << " entries" << std::endl;
            if (!torielDialogue->entries.empty()) {
                std::cout << "First entry: \"" << torielDialogue->entries[0].text << "\"" << std::endl;
            }
        }
        
        auto froggitData = assetManager->GetMonster("Froggit");
        if (froggitData) {
            std::cout << "Successfully loaded Froggit data - HP: " << froggitData->hp << ", ATK: " << froggitData->attack << std::endl;
        }
        
        auto bandageData = assetManager->GetItem("Bandage");
        if (bandageData) {
            std::cout << "Successfully loaded Bandage data - Heal: " << bandageData->healAmount << " HP" << std::endl;
        }
        
        auto ruinsRoom = assetManager->GetRoom("ruins_entrance");
        if (ruinsRoom) {
            std::cout << "Successfully loaded Ruins entrance room data" << std::endl;
        }
    }
    
    std::cout << "\nJSON data system test completed!" << std::endl;

    // Test save system
    std::cout << "\nTesting save system..." << std::endl;
    auto saveManager = game.GetSaveManager();
    auto gameData = game.GetGameData();

    if (saveManager && gameData) {
        // Modify some game data
        gameData->GetSaveData().playerName = "Frisk";
        gameData->GetSaveData().level = 5;
        gameData->GetSaveData().maxHP = 30;
        gameData->GetSaveData().hp = 25;
        gameData->GetSaveData().gold = 100;
        gameData->AddItem("Pie");
        gameData->SetFlag(GameFlag::TORIEL_SPARED, true);

        std::cout << "Modified game data - Name: " << gameData->GetSaveData().playerName
                  << ", Level: " << gameData->GetSaveData().level
                  << ", Gold: " << gameData->GetSaveData().gold << std::endl;

        // Test saving
        if (saveManager->SaveGame(1, gameData->GetSaveData())) {
            std::cout << "Successfully saved game to slot 1!" << std::endl;

            // Reset data and test loading
            gameData->GetSaveData().Reset();
            std::cout << "Reset data - Name: " << gameData->GetSaveData().playerName
                      << ", Level: " << gameData->GetSaveData().level << std::endl;

            // Load the save
            SaveData loadedData;
            if (saveManager->LoadGame(1, loadedData)) {
                std::cout << "Successfully loaded game from slot 1!" << std::endl;
                std::cout << "Loaded data - Name: " << loadedData.playerName
                          << ", Level: " << loadedData.level
                          << ", Gold: " << loadedData.gold << std::endl;

                // Test Undertale-specific saves
                if (saveManager->SaveToFile0(loadedData)) {
                    std::cout << "Successfully saved to file0 (Undertale format)!" << std::endl;
                }
            }
        }
    }

    std::cout << "\nSave system test completed!" << std::endl;
    std::cout << "\nPress Enter to start game loop (will run for a few seconds)..." << std::endl;
    std::cin.get();
    
    // Run the game for a short time to test the loop
    std::cout << "Starting game loop..." << std::endl;
    
    // For testing, we'll run for just a few iterations
    for (int i = 0; i < 5; ++i) {
        game.Update(Game::DELTA_TIME);
        game.Render();
        
        // Sleep for a bit to see the output
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    std::cout << "\nTest completed! Shutting down..." << std::endl;
    game.Shutdown();
    
    std::cout << "Game ended successfully!" << std::endl;
    return 0;
}
