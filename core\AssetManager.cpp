#include "AssetManager.h"
#include "JsonParser.h"
#include "../game/UndertaleData.h"
#include <iostream>
#include <fstream>
#include <filesystem>

// Placeholder classes for now (will implement properly later)
class Sprite {
public:
    Sprite(const std::string& filename) : m_filename(filename) {}
    std::string GetFilename() const { return m_filename; }
private:
    std::string m_filename;
};

class Font {
public:
    Font(const std::string& filename, int size) : m_filename(filename), m_size(size) {}
    std::string GetFilename() const { return m_filename; }
    int GetSize() const { return m_size; }
private:
    std::string m_filename;
    int m_size;
};

class AudioClip {
public:
    AudioClip(const std::string& filename) : m_filename(filename) {}
    std::string GetFilename() const { return m_filename; }
private:
    std::string m_filename;
};

class MusicTrack {
public:
    MusicTrack(const std::string& filename) : m_filename(filename) {}
    std::string GetFilename() const { return m_filename; }
private:
    std::string m_filename;
};

AssetManager::AssetManager() : m_initialized(false) {
}

AssetManager::~AssetManager() {
    Shutdown();
}

bool AssetManager::Initialize() {
    std::cout << "Initializing Asset Manager..." << std::endl;
    
    // Create asset directories if they don't exist
    std::filesystem::create_directories("assets/sprites");
    std::filesystem::create_directories("assets/audio");
    std::filesystem::create_directories("assets/data");
    std::filesystem::create_directories("assets/fonts");
    
    m_initialized = true;
    std::cout << "Asset Manager initialized successfully!" << std::endl;
    return true;
}

void AssetManager::Shutdown() {
    if (m_initialized) {
        std::cout << "Shutting down Asset Manager..." << std::endl;
        
        m_sprites.clear();
        m_fonts.clear();
        m_sounds.clear();
        m_music.clear();
        m_dialogues.clear();
        m_monsters.clear();
        m_items.clear();
        m_rooms.clear();
        
        m_initialized = false;
    }
}

std::shared_ptr<Sprite> AssetManager::LoadSprite(const std::string& name, const std::string& filename) {
    if (!m_initialized) return nullptr;
    
    auto sprite = std::make_shared<Sprite>(filename);
    m_sprites[name] = sprite;
    std::cout << "Loaded sprite: " << name << " from " << filename << std::endl;
    return sprite;
}

std::shared_ptr<Sprite> AssetManager::GetSprite(const std::string& name) const {
    auto it = m_sprites.find(name);
    return (it != m_sprites.end()) ? it->second : nullptr;
}

void AssetManager::UnloadSprite(const std::string& name) {
    m_sprites.erase(name);
}

std::shared_ptr<Font> AssetManager::LoadFont(const std::string& name, const std::string& filename, int size) {
    if (!m_initialized) return nullptr;
    
    auto font = std::make_shared<Font>(filename, size);
    m_fonts[name] = font;
    std::cout << "Loaded font: " << name << " from " << filename << std::endl;
    return font;
}

std::shared_ptr<Font> AssetManager::GetFont(const std::string& name) const {
    auto it = m_fonts.find(name);
    return (it != m_fonts.end()) ? it->second : nullptr;
}

void AssetManager::UnloadFont(const std::string& name) {
    m_fonts.erase(name);
}

std::shared_ptr<AudioClip> AssetManager::LoadSound(const std::string& name, const std::string& filename) {
    if (!m_initialized) return nullptr;
    
    auto sound = std::make_shared<AudioClip>(filename);
    m_sounds[name] = sound;
    std::cout << "Loaded sound: " << name << " from " << filename << std::endl;
    return sound;
}

std::shared_ptr<AudioClip> AssetManager::GetSound(const std::string& name) const {
    auto it = m_sounds.find(name);
    return (it != m_sounds.end()) ? it->second : nullptr;
}

std::shared_ptr<MusicTrack> AssetManager::LoadMusic(const std::string& name, const std::string& filename) {
    if (!m_initialized) return nullptr;
    
    auto music = std::make_shared<MusicTrack>(filename);
    m_music[name] = music;
    std::cout << "Loaded music: " << name << " from " << filename << std::endl;
    return music;
}

std::shared_ptr<MusicTrack> AssetManager::GetMusic(const std::string& name) const {
    auto it = m_music.find(name);
    return (it != m_music.end()) ? it->second : nullptr;
}

bool AssetManager::LoadDialogueData(const std::string& filename) {
    if (!m_initialized) return false;
    
    std::unordered_map<std::string, DialogueData> dialogues;
    if (!JsonDataLoader::LoadDialogueData(filename, dialogues)) {
        return false;
    }
    
    for (const auto& pair : dialogues) {
        m_dialogues[pair.first] = std::make_unique<DialogueData>(pair.second);
    }
    
    std::cout << "Loaded dialogue data from: " << filename << std::endl;
    return true;
}

bool AssetManager::LoadMonsterData(const std::string& filename) {
    if (!m_initialized) return false;
    
    std::unordered_map<std::string, MonsterData> monsters;
    if (!JsonDataLoader::LoadMonsterData(filename, monsters)) {
        return false;
    }
    
    for (const auto& pair : monsters) {
        m_monsters[pair.first] = std::make_unique<MonsterData>(pair.second);
    }
    
    std::cout << "Loaded monster data from: " << filename << std::endl;
    return true;
}

bool AssetManager::LoadItemData(const std::string& filename) {
    if (!m_initialized) return false;
    
    std::unordered_map<std::string, ItemData> items;
    if (!JsonDataLoader::LoadItemData(filename, items)) {
        return false;
    }
    
    for (const auto& pair : items) {
        m_items[pair.first] = std::make_unique<ItemData>(pair.second);
    }
    
    std::cout << "Loaded item data from: " << filename << std::endl;
    return true;
}

bool AssetManager::LoadRoomData(const std::string& filename) {
    if (!m_initialized) return false;
    
    std::unordered_map<std::string, RoomData> rooms;
    if (!JsonDataLoader::LoadRoomData(filename, rooms)) {
        return false;
    }
    
    for (const auto& pair : rooms) {
        m_rooms[pair.first] = std::make_unique<RoomData>(pair.second);
    }
    
    std::cout << "Loaded room data from: " << filename << std::endl;
    return true;
}

const DialogueData* AssetManager::GetDialogue(const std::string& key) const {
    auto it = m_dialogues.find(key);
    return (it != m_dialogues.end()) ? it->second.get() : nullptr;
}

const MonsterData* AssetManager::GetMonster(const std::string& name) const {
    auto it = m_monsters.find(name);
    return (it != m_monsters.end()) ? it->second.get() : nullptr;
}

const ItemData* AssetManager::GetItem(const std::string& name) const {
    auto it = m_items.find(name);
    return (it != m_items.end()) ? it->second.get() : nullptr;
}

const RoomData* AssetManager::GetRoom(const std::string& name) const {
    auto it = m_rooms.find(name);
    return (it != m_rooms.end()) ? it->second.get() : nullptr;
}

bool AssetManager::LoadUndertaleAssets() {
    std::cout << "Loading Undertale assets..." << std::endl;
    
    bool success = true;
    success &= LoadGameData();
    success &= LoadCharacterSprites();
    success &= LoadUISprites();
    success &= LoadBattleSprites();
    success &= LoadOverworldSprites();
    success &= LoadFonts();
    success &= LoadAudio();
    
    if (success) {
        std::cout << "All Undertale assets loaded successfully!" << std::endl;
    } else {
        std::cout << "Some assets failed to load." << std::endl;
    }
    
    return success;
}

bool AssetManager::LoadGameData() {
    std::cout << "Loading game data..." << std::endl;
    
    bool success = true;
    
    // Load dialogue files
    std::vector<std::string> dialogueFiles = {
        "assets/data/dialogue/toriel_intro.json",
        "assets/data/dialogue/flowey_intro.json"
    };
    
    for (const auto& file : dialogueFiles) {
        if (FileExists(file)) {
            success &= LoadDialogueData(file);
        }
    }
    
    // Load monster files
    std::vector<std::string> monsterFiles = {
        "assets/data/monsters/froggit.json",
        "assets/data/monsters/toriel.json"
    };
    
    for (const auto& file : monsterFiles) {
        if (FileExists(file)) {
            success &= LoadMonsterData(file);
        }
    }
    
    // Load item files
    std::vector<std::string> itemFiles = {
        "assets/data/items/bandage.json",
        "assets/data/items/stick.json"
    };
    
    for (const auto& file : itemFiles) {
        if (FileExists(file)) {
            success &= LoadItemData(file);
        }
    }
    
    // Load room files
    std::vector<std::string> roomFiles = {
        "assets/data/rooms/ruins_entrance.json",
        "assets/data/rooms/toriel_home.json"
    };
    
    for (const auto& file : roomFiles) {
        if (FileExists(file)) {
            success &= LoadRoomData(file);
        }
    }
    
    return success;
}

bool AssetManager::LoadCharacterSprites() {
    std::cout << "Loading character sprites..." << std::endl;

    // Placeholder - will load actual sprites later
    LoadSprite("frisk_overworld", "assets/sprites/characters/frisk_overworld.png");
    LoadSprite("toriel_overworld", "assets/sprites/characters/toriel_overworld.png");
    LoadSprite("flowey_overworld", "assets/sprites/characters/flowey_overworld.png");
    LoadSprite("sans_overworld", "assets/sprites/characters/sans_overworld.png");
    LoadSprite("papyrus_overworld", "assets/sprites/characters/papyrus_overworld.png");

    return true;
}

bool AssetManager::LoadUISprites() {
    std::cout << "Loading UI sprites..." << std::endl;

    // Placeholder - will load actual sprites later
    LoadSprite("ui_heart", "assets/sprites/ui/heart.png");
    LoadSprite("ui_battle_box", "assets/sprites/ui/battle_box.png");
    LoadSprite("ui_menu_bg", "assets/sprites/ui/menu_bg.png");
    LoadSprite("ui_text_box", "assets/sprites/ui/text_box.png");

    return true;
}

bool AssetManager::LoadBattleSprites() {
    std::cout << "Loading battle sprites..." << std::endl;

    // Placeholder - will load actual sprites later
    LoadSprite("froggit_battle", "assets/sprites/battle/froggit_battle.png");
    LoadSprite("toriel_battle", "assets/sprites/battle/toriel_battle.png");
    LoadSprite("bullet_white", "assets/sprites/battle/bullet_white.png");
    LoadSprite("bullet_blue", "assets/sprites/battle/bullet_blue.png");
    LoadSprite("bullet_orange", "assets/sprites/battle/bullet_orange.png");

    return true;
}

bool AssetManager::LoadOverworldSprites() {
    std::cout << "Loading overworld sprites..." << std::endl;

    // Placeholder - will load actual sprites later
    LoadSprite("bg_ruins_entrance", "assets/sprites/backgrounds/ruins_entrance.png");
    LoadSprite("bg_toriel_home", "assets/sprites/backgrounds/toriel_home.png");
    LoadSprite("save_point", "assets/sprites/overworld/save_point.png");

    return true;
}

bool AssetManager::LoadFonts() {
    std::cout << "Loading fonts..." << std::endl;

    // Placeholder - will load actual fonts later
    LoadFont("determination", "assets/fonts/determination.ttf", 16);
    LoadFont("comic_sans", "assets/fonts/comic_sans.ttf", 16);
    LoadFont("papyrus", "assets/fonts/papyrus.ttf", 16);

    return true;
}

bool AssetManager::LoadAudio() {
    std::cout << "Loading audio..." << std::endl;

    // Placeholder - will load actual audio later
    LoadMusic("ruins", "assets/audio/music/ruins.ogg");
    LoadMusic("home", "assets/audio/music/home.ogg");
    LoadMusic("battle", "assets/audio/music/battle.ogg");

    LoadSound("voice_toriel", "assets/audio/sfx/voice_toriel.wav");
    LoadSound("voice_flowey", "assets/audio/sfx/voice_flowey.wav");
    LoadSound("menu_select", "assets/audio/sfx/menu_select.wav");

    return true;
}

bool AssetManager::ValidateAssets() const {
    std::cout << "Validating assets..." << std::endl;

    bool valid = true;

    // Check if essential assets are loaded
    if (m_sprites.empty()) {
        std::cout << "Warning: No sprites loaded!" << std::endl;
        valid = false;
    }

    if (m_dialogues.empty()) {
        std::cout << "Warning: No dialogue data loaded!" << std::endl;
        valid = false;
    }

    if (m_monsters.empty()) {
        std::cout << "Warning: No monster data loaded!" << std::endl;
        valid = false;
    }

    return valid;
}

void AssetManager::ListLoadedAssets() const {
    std::cout << "\n=== Loaded Assets ===" << std::endl;
    std::cout << "Sprites: " << m_sprites.size() << std::endl;
    std::cout << "Fonts: " << m_fonts.size() << std::endl;
    std::cout << "Sounds: " << m_sounds.size() << std::endl;
    std::cout << "Music: " << m_music.size() << std::endl;
    std::cout << "Dialogues: " << m_dialogues.size() << std::endl;
    std::cout << "Monsters: " << m_monsters.size() << std::endl;
    std::cout << "Items: " << m_items.size() << std::endl;
    std::cout << "Rooms: " << m_rooms.size() << std::endl;
    std::cout << "===================" << std::endl;
}

bool AssetManager::FileExists(const std::string& filename) const {
    std::ifstream file(filename);
    return file.good();
}

std::vector<uint8_t> AssetManager::LoadFile(const std::string& filename) const {
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        return {};
    }

    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<uint8_t> data(size);
    file.read(reinterpret_cast<char*>(data.data()), size);

    return data;
}

std::string AssetManager::LoadTextFile(const std::string& filename) const {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return "";
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    return content;
}
