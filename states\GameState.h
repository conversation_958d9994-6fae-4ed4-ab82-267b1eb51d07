#pragma once

// Forward declarations
class Game;
class Renderer;
class GameStateManager;

class GameState {
public:
    GameState(Game* game) : m_game(game), m_stateManager(nullptr) {}
    virtual ~GameState() = default;
    
    // Core state functions
    virtual bool Initialize() = 0;
    virtual void Enter() {}
    virtual void Exit() {}
    virtual void Update(float deltaTime) = 0;
    virtual void Render(Renderer* renderer) = 0;
    virtual void Shutdown() {}
    
    // Input handling
    virtual void HandleInput() {}
    
    // State manager reference
    void SetStateManager(GameStateManager* stateManager) { m_stateManager = stateManager; }
    
protected:
    Game* m_game;
    GameStateManager* m_stateManager;
};
