#pragma once

#include <array>
#include <vector>
#include <string>
#include "../math/Vector2.h"

enum class Key {
    // Arrow keys (Undertale's main controls)
    UP = 0,
    DOWN,
    LEFT,
    RIGHT,
    
    // Action keys
    Z,          // Confirm/Attack
    X,          // Cancel/Back
    C,          // Menu
    
    // Additional keys
    ENTER,
    ESCAPE,
    SPACE,
    SHIFT,
    CTRL,
    ALT,
    
    // Function keys for debug
    F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12,
    
    // Numbers
    NUM_0, NUM_1, NUM_2, NUM_3, NUM_4, NUM_5, NUM_6, NUM_7, NUM_8, NUM_9,
    
    // Letters
    A, B, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, Y,
    
    COUNT
};

struct InputState {
    bool pressed;
    bool held;
    bool released;
    float holdTime;
    
    InputState() : pressed(false), held(false), released(false), holdTime(0.0f) {}
};

class InputManager {
public:
    InputManager();
    ~InputManager();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // Key state queries
    bool IsKeyPressed(Key key) const;
    bool IsKeyHeld(Key key) const;
    bool IsKeyReleased(Key key) const;
    float GetKeyHoldTime(Key key) const;
    
    // Convenience functions for Undertale controls
    bool IsConfirmPressed() const { return IsKeyPressed(Key::Z) || IsKeyPressed(Key::ENTER); }
    bool IsCancelPressed() const { return IsKeyPressed(Key::X) || IsKeyPressed(Key::ESCAPE); }
    bool IsMenuPressed() const { return IsKeyPressed(Key::C); }
    
    Vector2 GetMovementInput() const;
    
    // System
    bool ShouldQuit() const { return m_shouldQuit; }
    
    // Text input for naming
    std::string GetTextInput() const { return m_textInput; }
    void ClearTextInput() { m_textInput.clear(); }
    
private:
    void ProcessSystemInput();
    void UpdateKeyStates(float deltaTime);
    
    std::array<InputState, static_cast<size_t>(Key::COUNT)> m_keyStates;
    std::array<bool, static_cast<size_t>(Key::COUNT)> m_previousKeyStates;
    
    bool m_shouldQuit;
    std::string m_textInput;
    
    // Platform-specific input handling
    void PollEvents();
    bool GetKeyState(Key key) const;

    bool m_initialized;
};
