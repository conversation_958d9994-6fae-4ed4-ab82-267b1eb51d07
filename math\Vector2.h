#pragma once

#include <cmath>

struct Vector2 {
    float x, y;
    
    // Constructors
    Vector2() : x(0.0f), y(0.0f) {}
    Vector2(float x, float y) : x(x), y(y) {}
    Vector2(float value) : x(value), y(value) {}
    
    // Arithmetic operators
    Vector2 operator+(const Vector2& other) const {
        return Vector2(x + other.x, y + other.y);
    }
    
    Vector2 operator-(const Vector2& other) const {
        return Vector2(x - other.x, y - other.y);
    }
    
    Vector2 operator*(float scalar) const {
        return Vector2(x * scalar, y * scalar);
    }
    
    Vector2 operator*(const Vector2& other) const {
        return Vector2(x * other.x, y * other.y);
    }
    
    Vector2 operator/(float scalar) const {
        return Vector2(x / scalar, y / scalar);
    }
    
    Vector2 operator-() const {
        return Vector2(-x, -y);
    }
    
    // Assignment operators
    Vector2& operator+=(const Vector2& other) {
        x += other.x;
        y += other.y;
        return *this;
    }
    
    Vector2& operator-=(const Vector2& other) {
        x -= other.x;
        y -= other.y;
        return *this;
    }
    
    Vector2& operator*=(float scalar) {
        x *= scalar;
        y *= scalar;
        return *this;
    }
    
    Vector2& operator/=(float scalar) {
        x /= scalar;
        y /= scalar;
        return *this;
    }
    
    // Comparison operators
    bool operator==(const Vector2& other) const {
        return std::abs(x - other.x) < 0.0001f && std::abs(y - other.y) < 0.0001f;
    }
    
    bool operator!=(const Vector2& other) const {
        return !(*this == other);
    }
    
    // Vector operations
    float Length() const {
        return std::sqrt(x * x + y * y);
    }
    
    float LengthSquared() const {
        return x * x + y * y;
    }
    
    Vector2 Normalized() const {
        float len = Length();
        if (len > 0.0001f) {
            return *this / len;
        }
        return Vector2(0.0f, 0.0f);
    }
    
    void Normalize() {
        *this = Normalized();
    }
    
    float Dot(const Vector2& other) const {
        return x * other.x + y * other.y;
    }
    
    float Cross(const Vector2& other) const {
        return x * other.y - y * other.x;
    }
    
    float Distance(const Vector2& other) const {
        return (*this - other).Length();
    }
    
    float DistanceSquared(const Vector2& other) const {
        return (*this - other).LengthSquared();
    }
    
    Vector2 Lerp(const Vector2& target, float t) const {
        return *this + (target - *this) * t;
    }
    
    // Static constants
    static const Vector2 ZERO;
    static const Vector2 ONE;
    static const Vector2 UP;
    static const Vector2 DOWN;
    static const Vector2 LEFT;
    static const Vector2 RIGHT;
};

// Global operators
inline Vector2 operator*(float scalar, const Vector2& vector) {
    return vector * scalar;
}
