#pragma once

#include <vector>
#include <string>
#include <memory>
#include "../math/Vector2.h"
#include "../math/Color.h"

// Forward declarations
class Sprite;
class Font;

struct RenderCommand {
    enum Type {
        CLEAR,
        DRAW_SPRITE,
        DRAW_TEXT,
        DRAW_RECT,
        DRAW_LINE,
        DRAW_PIXEL
    };
    
    Type type;
    Vector2 position;
    Vector2 size;
    Color color;
    std::shared_ptr<Sprite> sprite;
    std::shared_ptr<Font> font;
    std::string text;
    float rotation;
    Vector2 scale;
    bool flipX, flipY;
    
    RenderCommand() : type(CLEAR), rotation(0.0f), scale(1.0f, 1.0f), flipX(false), flipY(false) {}
};

class Renderer {
public:
    Renderer();
    ~Renderer();
    
    bool Initialize(int width, int height);
    void Shutdown();
    
    void Clear(const Color& color = Color::BLACK);
    void Present();
    
    // Drawing functions
    void DrawSprite(std::shared_ptr<Sprite> sprite, const Vector2& position, 
                   const Vector2& scale = Vector2(1.0f, 1.0f), float rotation = 0.0f,
                   bool flipX = false, bool flipY = false, const Color& tint = Color::WHITE);
    
    void DrawText(std::shared_ptr<Font> font, const std::string& text, 
                 const Vector2& position, const Color& color = Color::WHITE);
    
    void DrawRect(const Vector2& position, const Vector2& size, 
                 const Color& color, bool filled = true);
    
    void DrawLine(const Vector2& start, const Vector2& end, const Color& color);
    void DrawPixel(const Vector2& position, const Color& color);
    
    // Screen properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    Vector2 GetSize() const { return Vector2(static_cast<float>(m_width), static_cast<float>(m_height)); }
    
    // Camera/viewport
    void SetCamera(const Vector2& position) { m_cameraPosition = position; }
    Vector2 GetCamera() const { return m_cameraPosition; }
    
private:
    void ExecuteRenderCommands();
    void RenderSoftware();
    
    int m_width, m_height;
    Vector2 m_cameraPosition;
    std::vector<RenderCommand> m_renderCommands;
    
    // Software rendering buffer (since no external libraries)
    std::vector<Color> m_frameBuffer;
    
    bool m_initialized;
};
