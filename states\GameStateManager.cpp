#include "GameStateManager.h"
#include "GameState.h"
#include "../Game.h"
#include <iostream>

GameStateManager::GameStateManager() : m_game(nullptr), m_initialized(false) {
}

GameStateManager::~GameStateManager() {
    Shutdown();
}

bool GameStateManager::Initialize(Game* game) {
    std::cout << "Initializing Game State Manager..." << std::endl;
    m_game = game;
    m_initialized = true;
    
    // Start with intro state (placeholder)
    // PushState(StateType::INTRO);
    
    std::cout << "Game State Manager initialized successfully!" << std::endl;
    return true;
}

void GameStateManager::Update(float deltaTime) {
    if (!m_initialized || m_stateStack.empty()) return;
    
    auto currentState = m_stateStack.top().get();
    if (currentState) {
        currentState->Update(deltaTime);
    }
}

void GameStateManager::Render(Renderer* renderer) {
    if (!m_initialized || m_stateStack.empty()) return;
    
    auto currentState = m_stateStack.top().get();
    if (currentState) {
        currentState->Render(renderer);
    }
}

void GameStateManager::Shutdown() {
    if (m_initialized) {
        std::cout << "Shutting down Game State Manager..." << std::endl;
        ClearStates();
        m_stateCache.clear();
        m_initialized = false;
    }
}

void GameStateManager::PushState(StateType type) {
    auto state = CreateState(type);
    if (state) {
        state->SetStateManager(this);
        state->Initialize();
        state->Enter();
        m_stateStack.push(std::move(state));
    }
}

void GameStateManager::PopState() {
    if (!m_stateStack.empty()) {
        m_stateStack.top()->Exit();
        m_stateStack.pop();
    }
}

void GameStateManager::ChangeState(StateType type) {
    ClearStates();
    PushState(type);
}

void GameStateManager::ClearStates() {
    while (!m_stateStack.empty()) {
        PopState();
    }
}

GameState* GameStateManager::GetCurrentState() const {
    if (m_stateStack.empty()) return nullptr;
    return m_stateStack.top().get();
}

StateType GameStateManager::GetCurrentStateType() const {
    // TODO: Implement state type tracking
    return StateType::INTRO;
}

bool GameStateManager::HasState(StateType type) const {
    // TODO: Implement state checking
    return false;
}

std::unique_ptr<GameState> GameStateManager::CreateState(StateType type) {
    // TODO: Implement actual state creation
    // For now, return nullptr as we haven't implemented specific states yet
    std::cout << "Creating state (placeholder): " << static_cast<int>(type) << std::endl;
    return nullptr;
}
