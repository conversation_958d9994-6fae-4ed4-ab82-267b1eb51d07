#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include "../math/Vector2.h"
#include "../math/Color.h"

// Dialogue system
struct DialogueEntry {
    std::string speaker;
    std::string text;
    std::string portrait;
    std::string sound;
    float speed;
    Color textColor;
    bool shaking;
    std::vector<std::string> choices;
    
    DialogueEntry() : speed(1.0f), textColor(Color::WHITE), shaking(false) {}
};

struct DialogueData {
    std::string key;
    std::vector<DialogueEntry> entries;
    std::unordered_map<std::string, std::vector<DialogueEntry>> branches;
    std::string nextDialogue;
    std::string condition;
};

// Monster/Enemy data
enum class MonsterType {
    FROGGIT,
    WHIMSUN,
    MOLDSMAL,
    LOOX,
    VEGETOID,
    MIGOSP,
    NAPSTABLOOK,
    TORIEL,
    DOGGO,
    DOGAMY_DOGARESSA,
    GREATER_DOG,
    JERRY,
    GYFTROT,
    PAPYRUS,
    SHYREN,
    AARON,
    WOSHUA,
    TEMMIE,
    MAD_DUMMY,
    UNDYNE,
    VULKIN,
    TSUNDERPLANE,
    PYROPE,
    ROYAL_GUARD,
    SO_SORRY,
    METTATON,
    METTATON_EX,
    METTATON_NEO,
    AMALGAMATE,
    FLOWEY,
    ASRIEL,
    SANS
};

struct AttackPattern {
    std::string name;
    float duration;
    std::string scriptFile;
    int difficulty;
    std::vector<std::string> bulletTypes;
};

struct MonsterData {
    MonsterType type;
    std::string name;
    std::string checkText;
    int hp;
    int attack;
    int defense;
    int exp;
    int gold;
    
    std::vector<std::string> acts;
    std::vector<AttackPattern> attacks;
    std::vector<std::string> flavorTexts;
    std::vector<std::string> spareConditions;
    
    std::string sprite;
    std::string battleSprite;
    Vector2 spriteOffset;
    
    bool canSpare;
    bool canFlee;
    bool isBoss;
    
    MonsterData() : hp(1), attack(1), defense(0), exp(0), gold(0), 
                   canSpare(true), canFlee(true), isBoss(false) {}
};

// Item data
enum class ItemType {
    CONSUMABLE,
    WEAPON,
    ARMOR,
    KEY_ITEM
};

struct ItemData {
    ItemType type;
    std::string name;
    std::string description;
    std::string useText;
    int healAmount;
    int attackBonus;
    int defenseBonus;
    int value;
    bool sellable;
    bool usableInBattle;
    bool usableInOverworld;
    std::string sprite;
    
    ItemData() : type(ItemType::CONSUMABLE), healAmount(0), attackBonus(0), 
                defenseBonus(0), value(0), sellable(true), 
                usableInBattle(true), usableInOverworld(true) {}
};

// Room/Area data
enum class RoomType {
    RUINS,
    SNOWDIN,
    WATERFALL,
    HOTLAND,
    CORE,
    NEW_HOME,
    TRUE_LAB,
    SPECIAL
};

struct Warp {
    std::string targetRoom;
    Vector2 targetPosition;
    Vector2 position;
    Vector2 size;
    std::string condition;
};

struct NPC {
    std::string name;
    Vector2 position;
    std::string sprite;
    std::string dialogueKey;
    bool canMove;
    std::vector<Vector2> movePath;
    std::string condition;
};

struct Encounter {
    std::vector<MonsterType> monsters;
    float probability;
    std::string condition;
    Vector2 area;  // Area where this encounter can happen
};

struct RoomData {
    std::string name;
    RoomType type;
    std::string backgroundSprite;
    std::string music;
    Vector2 size;
    Vector2 playerStartPos;
    
    std::vector<Warp> warps;
    std::vector<NPC> npcs;
    std::vector<Encounter> encounters;
    std::vector<std::string> items;  // Items that can be found in this room
    
    bool savePoint;
    Vector2 savePointPos;
    
    std::string script;  // Special room script
    std::string condition;  // Condition for room to be accessible
};

// Battle system data
enum class BulletType {
    WHITE,      // Normal bullets
    BLUE,       // Must be still
    ORANGE,     // Must be moving
    GREEN,      // Healing
    YELLOW,     // Directional
    PURPLE,     // Gravity
    CYAN        // Patience
};

struct Bullet {
    BulletType type;
    Vector2 position;
    Vector2 velocity;
    Vector2 size;
    float rotation;
    int damage;
    std::string sprite;
    float lifetime;
    
    Bullet() : rotation(0.0f), damage(1), lifetime(10.0f) {}
};

// Fun value events
struct FunEvent {
    int funValue;
    std::string eventType;
    std::string description;
    std::string triggerCondition;
    bool oneTime;
    bool triggered;
    
    FunEvent() : funValue(0), oneTime(true), triggered(false) {}
};
