#include "Renderer.h"
#include <iostream>
#include <algorithm>

Renderer::Renderer() : m_width(0), m_height(0), m_initialized(false) {
}

Renderer::~Renderer() {
    Shutdown();
}

bool Renderer::Initialize(int width, int height) {
    std::cout << "Initializing renderer (" << width << "x" << height << ")..." << std::endl;
    
    m_width = width;
    m_height = height;
    m_frameBuffer.resize(width * height, Color::BLACK);
    m_cameraPosition = Vector2::ZERO;
    
    m_initialized = true;
    std::cout << "Renderer initialized successfully!" << std::endl;
    return true;
}

void Renderer::Shutdown() {
    if (m_initialized) {
        std::cout << "Shutting down renderer..." << std::endl;
        m_frameBuffer.clear();
        m_renderCommands.clear();
        m_initialized = false;
    }
}

void Renderer::Clear(const Color& color) {
    if (!m_initialized) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::CLEAR;
    cmd.color = color;
    m_renderCommands.push_back(cmd);
}

void Renderer::Present() {
    if (!m_initialized) return;
    
    ExecuteRenderCommands();
    RenderSoftware();
    m_renderCommands.clear();
}

void Renderer::DrawSprite(std::shared_ptr<Sprite> sprite, const Vector2& position, 
                         const Vector2& scale, float rotation, bool flipX, bool flipY, const Color& tint) {
    if (!m_initialized || !sprite) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::DRAW_SPRITE;
    cmd.sprite = sprite;
    cmd.position = position;
    cmd.scale = scale;
    cmd.rotation = rotation;
    cmd.flipX = flipX;
    cmd.flipY = flipY;
    cmd.color = tint;
    m_renderCommands.push_back(cmd);
}

void Renderer::DrawText(std::shared_ptr<Font> font, const std::string& text, 
                       const Vector2& position, const Color& color) {
    if (!m_initialized || !font) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::DRAW_TEXT;
    cmd.font = font;
    cmd.text = text;
    cmd.position = position;
    cmd.color = color;
    m_renderCommands.push_back(cmd);
}

void Renderer::DrawRect(const Vector2& position, const Vector2& size, 
                       const Color& color, bool filled) {
    if (!m_initialized) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::DRAW_RECT;
    cmd.position = position;
    cmd.size = size;
    cmd.color = color;
    m_renderCommands.push_back(cmd);
}

void Renderer::DrawLine(const Vector2& start, const Vector2& end, const Color& color) {
    if (!m_initialized) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::DRAW_LINE;
    cmd.position = start;
    cmd.size = end;  // Using size to store end position
    cmd.color = color;
    m_renderCommands.push_back(cmd);
}

void Renderer::DrawPixel(const Vector2& position, const Color& color) {
    if (!m_initialized) return;
    
    RenderCommand cmd;
    cmd.type = RenderCommand::DRAW_PIXEL;
    cmd.position = position;
    cmd.color = color;
    m_renderCommands.push_back(cmd);
}

void Renderer::ExecuteRenderCommands() {
    for (const auto& cmd : m_renderCommands) {
        switch (cmd.type) {
            case RenderCommand::CLEAR:
                std::fill(m_frameBuffer.begin(), m_frameBuffer.end(), cmd.color);
                break;
                
            case RenderCommand::DRAW_RECT: {
                int x1 = static_cast<int>(cmd.position.x - m_cameraPosition.x);
                int y1 = static_cast<int>(cmd.position.y - m_cameraPosition.y);
                int x2 = x1 + static_cast<int>(cmd.size.x);
                int y2 = y1 + static_cast<int>(cmd.size.y);
                
                for (int y = std::max(0, y1); y < std::min(m_height, y2); ++y) {
                    for (int x = std::max(0, x1); x < std::min(m_width, x2); ++x) {
                        m_frameBuffer[y * m_width + x] = cmd.color;
                    }
                }
                break;
            }
            
            case RenderCommand::DRAW_PIXEL: {
                int x = static_cast<int>(cmd.position.x - m_cameraPosition.x);
                int y = static_cast<int>(cmd.position.y - m_cameraPosition.y);
                
                if (x >= 0 && x < m_width && y >= 0 && y < m_height) {
                    m_frameBuffer[y * m_width + x] = cmd.color;
                }
                break;
            }
            
            // TODO: Implement sprite and text rendering
            case RenderCommand::DRAW_SPRITE:
            case RenderCommand::DRAW_TEXT:
            case RenderCommand::DRAW_LINE:
                // Placeholder - will implement when we have sprite and font classes
                break;
        }
    }
}

void Renderer::RenderSoftware() {
    // For now, just output a simple representation to console
    // In a real implementation, this would output to a window or framebuffer
    static int frameCount = 0;
    if (frameCount % 30 == 0) {  // Only print every 30 frames
        std::cout << "Frame " << frameCount << " rendered (" << m_width << "x" << m_height << ")" << std::endl;
    }
    frameCount++;
}
