#pragma once

#include <string>
#include <vector>
#include "../game/GameData.h"

struct SaveSlot {
    bool exists;
    SaveData data;
    std::string filename;
    std::string timestamp;
    
    SaveSlot() : exists(false) {}
};

class SaveManager {
public:
    SaveManager();
    ~SaveManager();
    
    bool Initialize();
    void Shutdown();
    
    // Save operations
    bool SaveGame(int slot, const SaveData& data);
    bool LoadGame(int slot, SaveData& data);
    bool DeleteSave(int slot);
    bool SaveExists(int slot) const;
    
    // Save slot management
    std::vector<SaveSlot> GetSaveSlots() const;
    SaveSlot GetSaveSlot(int slot) const;
    int GetEmptySlot() const;
    int GetSaveCount() const;
    
    // Quick save/load (slot 0)
    bool QuickSave(const SaveData& data);
    bool QuickLoad(SaveData& data);
    bool HasQuickSave() const;
    
    // Auto save functionality
    bool AutoSave(const SaveData& data);
    bool LoadAutoSave(SaveData& data);
    bool HasAutoSave() const;
    
    // Settings save/load
    bool SaveSettings(const std::string& settings);
    std::string LoadSettings() const;
    
    // Undertale-specific save features
    bool SaveToFile0(const SaveData& data);  // file0 (main save)
    bool LoadFromFile0(SaveData& data);
    bool SaveToFile9(const SaveData& data);  // file9 (genocide flag)
    bool LoadFromFile9(SaveData& data);
    bool DeleteFile0();
    bool DeleteFile9();
    bool File0Exists() const;
    bool File9Exists() const;
    
    // Save validation
    bool ValidateSave(const SaveData& data) const;
    bool VerifySaveIntegrity(int slot) const;
    
    // Backup functionality
    bool CreateBackup(int slot);
    bool RestoreBackup(int slot);
    bool HasBackup(int slot) const;
    
private:
    std::string GetSaveFilename(int slot) const;
    std::string GetBackupFilename(int slot) const;
    std::string GetSettingsFilename() const;
    
    bool WriteSaveData(const std::string& filename, const SaveData& data);
    bool ReadSaveData(const std::string& filename, SaveData& data);
    
    std::string SerializeSaveData(const SaveData& data) const;
    bool DeserializeSaveData(const std::string& data, SaveData& saveData) const;
    
    std::string CalculateChecksum(const SaveData& data) const;
    bool VerifyChecksum(const SaveData& data, const std::string& checksum) const;
    
    std::string GetTimestamp() const;
    bool FileExists(const std::string& filename) const;
    bool DeleteFile(const std::string& filename) const;
    
    static constexpr int MAX_SAVE_SLOTS = 10;
    static constexpr int QUICK_SAVE_SLOT = 0;
    static constexpr int AUTO_SAVE_SLOT = -1;
    
    std::string m_saveDirectory;
    bool m_initialized;
};
