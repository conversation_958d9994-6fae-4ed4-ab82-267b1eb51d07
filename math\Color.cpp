#include "Color.h"

// Basic colors
const Color Color::BLACK(0.0f, 0.0f, 0.0f, 1.0f);
const Color Color::WHITE(1.0f, 1.0f, 1.0f, 1.0f);
const Color Color::RED(1.0f, 0.0f, 0.0f, 1.0f);
const Color Color::GREEN(0.0f, 1.0f, 0.0f, 1.0f);
const Color Color::BLUE(0.0f, 0.0f, 1.0f, 1.0f);
const Color Color::YELLOW(1.0f, 1.0f, 0.0f, 1.0f);
const Color Color::CYAN(0.0f, 1.0f, 1.0f, 1.0f);
const Color Color::MAGENTA(1.0f, 0.0f, 1.0f, 1.0f);
const Color Color::ORANGE(1.0f, 0.5f, 0.0f, 1.0f);
const Color Color::PURPLE(0.5f, 0.0f, 1.0f, 1.0f);

// Undertale SOUL colors
const Color Color::SOUL_RED(1.0f, 0.0f, 0.0f, 1.0f);
const Color Color::SOUL_BLUE(0.0f, 0.5f, 1.0f, 1.0f);
const Color Color::SOUL_ORANGE(1.0f, 0.5f, 0.0f, 1.0f);
const Color Color::SOUL_GREEN(0.0f, 1.0f, 0.0f, 1.0f);
const Color Color::SOUL_YELLOW(1.0f, 1.0f, 0.0f, 1.0f);
const Color Color::SOUL_PURPLE(0.8f, 0.0f, 1.0f, 1.0f);
const Color Color::SOUL_CYAN(0.0f, 1.0f, 1.0f, 1.0f);

// Undertale text colors
const Color Color::TEXT_WHITE(1.0f, 1.0f, 1.0f, 1.0f);
const Color Color::TEXT_YELLOW(1.0f, 1.0f, 0.0f, 1.0f);
const Color Color::TEXT_RED(1.0f, 0.0f, 0.0f, 1.0f);
const Color Color::TEXT_GREEN(0.0f, 1.0f, 0.0f, 1.0f);
const Color Color::TEXT_BLUE(0.0f, 0.5f, 1.0f, 1.0f);

// Undertale UI colors
const Color Color::UI_DARK(0.1f, 0.1f, 0.1f, 1.0f);
const Color Color::UI_LIGHT(0.9f, 0.9f, 0.9f, 1.0f);
const Color Color::UI_BORDER(0.5f, 0.5f, 0.5f, 1.0f);
