#include "JsonParser.h"
#include "../game/UndertaleData.h"
#include <fstream>
#include <sstream>
#include <iostream>
#include <cctype>

// JsonValue implementation
bool JsonValue::AsBool() const {
    if (m_type == BOOL) {
        return std::get<bool>(m_value);
    }
    return false;
}

double JsonValue::AsNumber() const {
    if (m_type == NUMBER) {
        return std::get<double>(m_value);
    }
    return 0.0;
}

std::string JsonValue::AsString() const {
    if (m_type == STRING) {
        return std::get<std::string>(m_value);
    }
    return "";
}

const JsonValue::JsonArray& JsonValue::AsArray() const {
    static JsonArray empty;
    if (m_type == ARRAY) {
        return std::get<JsonArray>(m_value);
    }
    return empty;
}

const JsonValue::JsonObject& JsonValue::AsObject() const {
    static JsonObject empty;
    if (m_type == OBJECT) {
        return std::get<JsonObject>(m_value);
    }
    return empty;
}

std::shared_ptr<JsonValue> JsonValue::operator[](const std::string& key) const {
    if (m_type == OBJECT) {
        const auto& obj = std::get<JsonObject>(m_value);
        auto it = obj.find(key);
        if (it != obj.end()) {
            return it->second;
        }
    }
    return std::make_shared<JsonValue>(); // Return null value
}

bool JsonValue::HasKey(const std::string& key) const {
    if (m_type == OBJECT) {
        const auto& obj = std::get<JsonObject>(m_value);
        return obj.find(key) != obj.end();
    }
    return false;
}

std::shared_ptr<JsonValue> JsonValue::operator[](size_t index) const {
    if (m_type == ARRAY) {
        const auto& arr = std::get<JsonArray>(m_value);
        if (index < arr.size()) {
            return arr[index];
        }
    }
    return std::make_shared<JsonValue>(); // Return null value
}

size_t JsonValue::Size() const {
    if (m_type == ARRAY) {
        return std::get<JsonArray>(m_value).size();
    } else if (m_type == OBJECT) {
        return std::get<JsonObject>(m_value).size();
    }
    return 0;
}

// JsonParser implementation
std::shared_ptr<JsonValue> JsonParser::Parse(const std::string& json) {
    size_t pos = 0;
    SkipWhitespace(json, pos);
    return ParseValue(json, pos);
}

std::shared_ptr<JsonValue> JsonParser::ParseFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open JSON file: " << filename << std::endl;
        return std::make_shared<JsonValue>();
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    return Parse(buffer.str());
}

std::shared_ptr<JsonValue> JsonParser::ParseValue(const std::string& json, size_t& pos) {
    SkipWhitespace(json, pos);
    
    if (pos >= json.length()) {
        return std::make_shared<JsonValue>();
    }
    
    char c = json[pos];
    
    if (c == '{') {
        return ParseObject(json, pos);
    } else if (c == '[') {
        return ParseArray(json, pos);
    } else if (c == '"') {
        return ParseString(json, pos);
    } else if (c == 't' || c == 'f') {
        return ParseBool(json, pos);
    } else if (c == 'n') {
        return ParseNull(json, pos);
    } else if (std::isdigit(c) || c == '-') {
        return ParseNumber(json, pos);
    }
    
    return std::make_shared<JsonValue>();
}

std::shared_ptr<JsonValue> JsonParser::ParseObject(const std::string& json, size_t& pos) {
    JsonValue::JsonObject obj;
    pos++; // Skip '{'
    
    SkipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == '}') {
        pos++; // Skip '}'
        return std::make_shared<JsonValue>(obj);
    }
    
    while (pos < json.length()) {
        SkipWhitespace(json, pos);
        
        // Parse key
        auto key = ParseString(json, pos);
        if (!key || !key->IsString()) {
            break;
        }
        
        SkipWhitespace(json, pos);
        
        // Expect ':'
        if (pos >= json.length() || json[pos] != ':') {
            break;
        }
        pos++; // Skip ':'
        
        // Parse value
        auto value = ParseValue(json, pos);
        obj[key->AsString()] = value;
        
        SkipWhitespace(json, pos);
        
        if (pos >= json.length()) {
            break;
        }
        
        if (json[pos] == '}') {
            pos++; // Skip '}'
            break;
        } else if (json[pos] == ',') {
            pos++; // Skip ','
        } else {
            break;
        }
    }
    
    return std::make_shared<JsonValue>(obj);
}

std::shared_ptr<JsonValue> JsonParser::ParseArray(const std::string& json, size_t& pos) {
    JsonValue::JsonArray arr;
    pos++; // Skip '['
    
    SkipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == ']') {
        pos++; // Skip ']'
        return std::make_shared<JsonValue>(arr);
    }
    
    while (pos < json.length()) {
        auto value = ParseValue(json, pos);
        arr.push_back(value);
        
        SkipWhitespace(json, pos);
        
        if (pos >= json.length()) {
            break;
        }
        
        if (json[pos] == ']') {
            pos++; // Skip ']'
            break;
        } else if (json[pos] == ',') {
            pos++; // Skip ','
            SkipWhitespace(json, pos);
        } else {
            break;
        }
    }
    
    return std::make_shared<JsonValue>(arr);
}

std::shared_ptr<JsonValue> JsonParser::ParseString(const std::string& json, size_t& pos) {
    if (pos >= json.length() || json[pos] != '"') {
        return std::make_shared<JsonValue>();
    }
    
    pos++; // Skip opening '"'
    size_t start = pos;
    
    while (pos < json.length() && json[pos] != '"') {
        if (json[pos] == '\\') {
            pos++; // Skip escape character
        }
        pos++;
    }
    
    if (pos >= json.length()) {
        return std::make_shared<JsonValue>();
    }
    
    std::string str = json.substr(start, pos - start);
    pos++; // Skip closing '"'
    
    return std::make_shared<JsonValue>(UnescapeString(str));
}

std::shared_ptr<JsonValue> JsonParser::ParseNumber(const std::string& json, size_t& pos) {
    size_t start = pos;
    
    if (json[pos] == '-') {
        pos++;
    }
    
    while (pos < json.length() && std::isdigit(json[pos])) {
        pos++;
    }
    
    if (pos < json.length() && json[pos] == '.') {
        pos++;
        while (pos < json.length() && std::isdigit(json[pos])) {
            pos++;
        }
    }
    
    std::string numStr = json.substr(start, pos - start);
    double value = std::stod(numStr);
    
    return std::make_shared<JsonValue>(value);
}

std::shared_ptr<JsonValue> JsonParser::ParseBool(const std::string& json, size_t& pos) {
    if (json.substr(pos, 4) == "true") {
        pos += 4;
        return std::make_shared<JsonValue>(true);
    } else if (json.substr(pos, 5) == "false") {
        pos += 5;
        return std::make_shared<JsonValue>(false);
    }
    
    return std::make_shared<JsonValue>();
}

std::shared_ptr<JsonValue> JsonParser::ParseNull(const std::string& json, size_t& pos) {
    if (json.substr(pos, 4) == "null") {
        pos += 4;
        return std::make_shared<JsonValue>();
    }
    
    return std::make_shared<JsonValue>();
}

void JsonParser::SkipWhitespace(const std::string& json, size_t& pos) {
    while (pos < json.length() && std::isspace(json[pos])) {
        pos++;
    }
}

std::string JsonParser::UnescapeString(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '\\' && i + 1 < str.length()) {
            switch (str[i + 1]) {
                case '"': result += '"'; i++; break;
                case '\\': result += '\\'; i++; break;
                case '/': result += '/'; i++; break;
                case 'b': result += '\b'; i++; break;
                case 'f': result += '\f'; i++; break;
                case 'n': result += '\n'; i++; break;
                case 'r': result += '\r'; i++; break;
                case 't': result += '\t'; i++; break;
                default: result += str[i]; break;
            }
        } else {
            result += str[i];
        }
    }
    return result;
}

std::string JsonParser::EscapeString(const std::string& str) {
    std::string result;
    for (char c : str) {
        switch (c) {
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default: result += c; break;
        }
    }
    return result;
}

// JsonDataLoader implementation
bool JsonDataLoader::LoadDialogueData(const std::string& filename, std::unordered_map<std::string, DialogueData>& dialogues) {
    auto json = JsonParser::ParseFromFile(filename);
    if (!json || !json->IsObject()) {
        std::cerr << "Failed to parse dialogue JSON: " << filename << std::endl;
        return false;
    }

    DialogueData dialogue = ParseDialogue(*json);
    dialogues[dialogue.key] = dialogue;
    return true;
}

bool JsonDataLoader::LoadMonsterData(const std::string& filename, std::unordered_map<std::string, MonsterData>& monsters) {
    auto json = JsonParser::ParseFromFile(filename);
    if (!json || !json->IsObject()) {
        std::cerr << "Failed to parse monster JSON: " << filename << std::endl;
        return false;
    }

    MonsterData monster = ParseMonster(*json);
    monsters[monster.name] = monster;
    return true;
}

bool JsonDataLoader::LoadItemData(const std::string& filename, std::unordered_map<std::string, ItemData>& items) {
    auto json = JsonParser::ParseFromFile(filename);
    if (!json || !json->IsObject()) {
        std::cerr << "Failed to parse item JSON: " << filename << std::endl;
        return false;
    }

    ItemData item = ParseItem(*json);
    items[item.name] = item;
    return true;
}

bool JsonDataLoader::LoadRoomData(const std::string& filename, std::unordered_map<std::string, RoomData>& rooms) {
    auto json = JsonParser::ParseFromFile(filename);
    if (!json || !json->IsObject()) {
        std::cerr << "Failed to parse room JSON: " << filename << std::endl;
        return false;
    }

    RoomData room = ParseRoom(*json);
    rooms[room.name] = room;
    return true;
}

DialogueData JsonDataLoader::ParseDialogue(const JsonValue& json) {
    DialogueData dialogue;

    if (json.HasKey("key")) {
        dialogue.key = json["key"]->AsString();
    }

    if (json.HasKey("entries") && json["entries"]->IsArray()) {
        const auto& entries = json["entries"]->AsArray();
        for (const auto& entryJson : entries) {
            DialogueEntry entry;
            if (entryJson->HasKey("speaker")) entry.speaker = entryJson->operator[]("speaker")->AsString();
            if (entryJson->HasKey("text")) entry.text = entryJson->operator[]("text")->AsString();
            if (entryJson->HasKey("portrait")) entry.portrait = entryJson->operator[]("portrait")->AsString();
            if (entryJson->HasKey("sound")) entry.sound = entryJson->operator[]("sound")->AsString();
            if (entryJson->HasKey("speed")) entry.speed = entryJson->operator[]("speed")->AsFloat();
            if (entryJson->HasKey("shaking")) entry.shaking = entryJson->operator[]("shaking")->AsBool();

            if (entryJson->HasKey("textColor") && entryJson->operator[]("textColor")->IsArray()) {
                const auto& colorArray = entryJson->operator[]("textColor")->AsArray();
                if (colorArray.size() >= 4) {
                    entry.textColor = Color(
                        static_cast<uint8_t>(colorArray[0]->AsInt()),
                        static_cast<uint8_t>(colorArray[1]->AsInt()),
                        static_cast<uint8_t>(colorArray[2]->AsInt()),
                        static_cast<uint8_t>(colorArray[3]->AsInt())
                    );
                }
            }

            if (entryJson->HasKey("choices") && entryJson->operator[]("choices")->IsArray()) {
                const auto& choicesArray = entryJson->operator[]("choices")->AsArray();
                for (const auto& choice : choicesArray) {
                    entry.choices.push_back(choice->AsString());
                }
            }

            dialogue.entries.push_back(entry);
        }
    }

    if (json.HasKey("nextDialogue")) {
        dialogue.nextDialogue = json["nextDialogue"]->AsString();
    }

    if (json.HasKey("condition")) {
        dialogue.condition = json["condition"]->AsString();
    }

    return dialogue;
}

MonsterData JsonDataLoader::ParseMonster(const JsonValue& json) {
    MonsterData monster;

    if (json.HasKey("name")) monster.name = json["name"]->AsString();
    if (json.HasKey("checkText")) monster.checkText = json["checkText"]->AsString();
    if (json.HasKey("hp")) monster.hp = json["hp"]->AsInt();
    if (json.HasKey("attack")) monster.attack = json["attack"]->AsInt();
    if (json.HasKey("defense")) monster.defense = json["defense"]->AsInt();
    if (json.HasKey("exp")) monster.exp = json["exp"]->AsInt();
    if (json.HasKey("gold")) monster.gold = json["gold"]->AsInt();
    if (json.HasKey("sprite")) monster.sprite = json["sprite"]->AsString();
    if (json.HasKey("battleSprite")) monster.battleSprite = json["battleSprite"]->AsString();
    if (json.HasKey("canSpare")) monster.canSpare = json["canSpare"]->AsBool();
    if (json.HasKey("canFlee")) monster.canFlee = json["canFlee"]->AsBool();
    if (json.HasKey("isBoss")) monster.isBoss = json["isBoss"]->AsBool();

    if (json.HasKey("acts") && json["acts"]->IsArray()) {
        const auto& actsArray = json["acts"]->AsArray();
        for (const auto& act : actsArray) {
            monster.acts.push_back(act->AsString());
        }
    }

    if (json.HasKey("flavorTexts") && json["flavorTexts"]->IsArray()) {
        const auto& textsArray = json["flavorTexts"]->AsArray();
        for (const auto& text : textsArray) {
            monster.flavorTexts.push_back(text->AsString());
        }
    }

    if (json.HasKey("spareConditions") && json["spareConditions"]->IsArray()) {
        const auto& conditionsArray = json["spareConditions"]->AsArray();
        for (const auto& condition : conditionsArray) {
            monster.spareConditions.push_back(condition->AsString());
        }
    }

    if (json.HasKey("spriteOffset") && json["spriteOffset"]->IsArray()) {
        const auto& offsetArray = json["spriteOffset"]->AsArray();
        if (offsetArray.size() >= 2) {
            monster.spriteOffset = Vector2(offsetArray[0]->AsFloat(), offsetArray[1]->AsFloat());
        }
    }

    return monster;
}

ItemData JsonDataLoader::ParseItem(const JsonValue& json) {
    ItemData item;

    if (json.HasKey("name")) item.name = json["name"]->AsString();
    if (json.HasKey("description")) item.description = json["description"]->AsString();
    if (json.HasKey("useText")) item.useText = json["useText"]->AsString();
    if (json.HasKey("healAmount")) item.healAmount = json["healAmount"]->AsInt();
    if (json.HasKey("attackBonus")) item.attackBonus = json["attackBonus"]->AsInt();
    if (json.HasKey("defenseBonus")) item.defenseBonus = json["defenseBonus"]->AsInt();
    if (json.HasKey("value")) item.value = json["value"]->AsInt();
    if (json.HasKey("sellable")) item.sellable = json["sellable"]->AsBool();
    if (json.HasKey("usableInBattle")) item.usableInBattle = json["usableInBattle"]->AsBool();
    if (json.HasKey("usableInOverworld")) item.usableInOverworld = json["usableInOverworld"]->AsBool();
    if (json.HasKey("sprite")) item.sprite = json["sprite"]->AsString();

    if (json.HasKey("type")) {
        std::string typeStr = json["type"]->AsString();
        if (typeStr == "CONSUMABLE") item.type = ItemType::CONSUMABLE;
        else if (typeStr == "WEAPON") item.type = ItemType::WEAPON;
        else if (typeStr == "ARMOR") item.type = ItemType::ARMOR;
        else if (typeStr == "KEY_ITEM") item.type = ItemType::KEY_ITEM;
    }

    return item;
}

RoomData JsonDataLoader::ParseRoom(const JsonValue& json) {
    RoomData room;

    if (json.HasKey("name")) room.name = json["name"]->AsString();
    if (json.HasKey("backgroundSprite")) room.backgroundSprite = json["backgroundSprite"]->AsString();
    if (json.HasKey("music")) room.music = json["music"]->AsString();
    if (json.HasKey("savePoint")) room.savePoint = json["savePoint"]->AsBool();
    if (json.HasKey("script")) room.script = json["script"]->AsString();
    if (json.HasKey("condition")) room.condition = json["condition"]->AsString();

    if (json.HasKey("type")) {
        std::string typeStr = json["type"]->AsString();
        if (typeStr == "RUINS") room.type = RoomType::RUINS;
        else if (typeStr == "SNOWDIN") room.type = RoomType::SNOWDIN;
        else if (typeStr == "WATERFALL") room.type = RoomType::WATERFALL;
        else if (typeStr == "HOTLAND") room.type = RoomType::HOTLAND;
        else if (typeStr == "CORE") room.type = RoomType::CORE;
        else if (typeStr == "NEW_HOME") room.type = RoomType::NEW_HOME;
        else if (typeStr == "TRUE_LAB") room.type = RoomType::TRUE_LAB;
        else if (typeStr == "SPECIAL") room.type = RoomType::SPECIAL;
    }

    if (json.HasKey("size") && json["size"]->IsArray()) {
        const auto& sizeArray = json["size"]->AsArray();
        if (sizeArray.size() >= 2) {
            room.size = Vector2(sizeArray[0]->AsFloat(), sizeArray[1]->AsFloat());
        }
    }

    if (json.HasKey("playerStartPos") && json["playerStartPos"]->IsArray()) {
        const auto& posArray = json["playerStartPos"]->AsArray();
        if (posArray.size() >= 2) {
            room.playerStartPos = Vector2(posArray[0]->AsFloat(), posArray[1]->AsFloat());
        }
    }

    if (json.HasKey("savePointPos") && json["savePointPos"]->IsArray()) {
        const auto& posArray = json["savePointPos"]->AsArray();
        if (posArray.size() >= 2) {
            room.savePointPos = Vector2(posArray[0]->AsFloat(), posArray[1]->AsFloat());
        }
    }

    return room;
}
