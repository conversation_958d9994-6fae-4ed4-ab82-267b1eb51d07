#pragma once

#include <cstdint>
#include <algorithm>

struct Color {
    float r, g, b, a;
    
    // Constructors
    Color() : r(0.0f), g(0.0f), b(0.0f), a(1.0f) {}
    Color(float r, float g, float b, float a = 1.0f) : r(r), g(g), b(b), a(a) {}
    Color(uint8_t r, uint8_t g, uint8_t b, uint8_t a = 255) 
        : r(r / 255.0f), g(g / 255.0f), b(b / 255.0f), a(a / 255.0f) {}
    Color(uint32_t rgba) {
        r = ((rgba >> 24) & 0xFF) / 255.0f;
        g = ((rgba >> 16) & 0xFF) / 255.0f;
        b = ((rgba >> 8) & 0xFF) / 255.0f;
        a = (rgba & 0xFF) / 255.0f;
    }
    
    // Arithmetic operators
    Color operator+(const Color& other) const {
        return Color(r + other.r, g + other.g, b + other.b, a + other.a);
    }
    
    Color operator-(const Color& other) const {
        return Color(r - other.r, g - other.g, b - other.b, a - other.a);
    }
    
    Color operator*(float scalar) const {
        return Color(r * scalar, g * scalar, b * scalar, a * scalar);
    }
    
    Color operator*(const Color& other) const {
        return Color(r * other.r, g * other.g, b * other.b, a * other.a);
    }
    
    // Assignment operators
    Color& operator+=(const Color& other) {
        r += other.r;
        g += other.g;
        b += other.b;
        a += other.a;
        return *this;
    }
    
    Color& operator-=(const Color& other) {
        r -= other.r;
        g -= other.g;
        b -= other.b;
        a -= other.a;
        return *this;
    }
    
    Color& operator*=(float scalar) {
        r *= scalar;
        g *= scalar;
        b *= scalar;
        a *= scalar;
        return *this;
    }
    
    // Comparison operators
    bool operator==(const Color& other) const {
        return r == other.r && g == other.g && b == other.b && a == other.a;
    }
    
    bool operator!=(const Color& other) const {
        return !(*this == other);
    }
    
    // Utility functions
    Color Clamped() const {
        return Color(
            std::clamp(r, 0.0f, 1.0f),
            std::clamp(g, 0.0f, 1.0f),
            std::clamp(b, 0.0f, 1.0f),
            std::clamp(a, 0.0f, 1.0f)
        );
    }
    
    void Clamp() {
        *this = Clamped();
    }
    
    Color Lerp(const Color& target, float t) const {
        return *this + (target - *this) * t;
    }
    
    uint32_t ToRGBA() const {
        uint8_t rByte = static_cast<uint8_t>(std::clamp(r * 255.0f, 0.0f, 255.0f));
        uint8_t gByte = static_cast<uint8_t>(std::clamp(g * 255.0f, 0.0f, 255.0f));
        uint8_t bByte = static_cast<uint8_t>(std::clamp(b * 255.0f, 0.0f, 255.0f));
        uint8_t aByte = static_cast<uint8_t>(std::clamp(a * 255.0f, 0.0f, 255.0f));
        
        return (rByte << 24) | (gByte << 16) | (bByte << 8) | aByte;
    }
    
    // Undertale color palette
    static const Color BLACK;
    static const Color WHITE;
    static const Color RED;
    static const Color GREEN;
    static const Color BLUE;
    static const Color YELLOW;
    static const Color CYAN;
    static const Color MAGENTA;
    static const Color ORANGE;
    static const Color PURPLE;
    
    // Undertale-specific colors
    static const Color SOUL_RED;        // Player SOUL color
    static const Color SOUL_BLUE;       // Blue attacks
    static const Color SOUL_ORANGE;     // Orange attacks
    static const Color SOUL_GREEN;      // Healing
    static const Color SOUL_YELLOW;     // Justice
    static const Color SOUL_PURPLE;     // Perseverance
    static const Color SOUL_CYAN;       // Patience
    
    static const Color TEXT_WHITE;      // Main text color
    static const Color TEXT_YELLOW;     // Important text/names
    static const Color TEXT_RED;        // Damage/danger
    static const Color TEXT_GREEN;      // Healing/positive
    static const Color TEXT_BLUE;       // Special text
    
    static const Color UI_DARK;         // Dark UI elements
    static const Color UI_LIGHT;        // Light UI elements
    static const Color UI_BORDER;       // UI borders
};

// Global operators
inline Color operator*(float scalar, const Color& color) {
    return color * scalar;
}
