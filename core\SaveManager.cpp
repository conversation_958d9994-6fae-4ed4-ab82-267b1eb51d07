#include "SaveManager.h"
#include "JsonParser.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <chrono>

SaveManager::SaveManager() : m_initialized(false), m_saveDirectory("saves") {
}

SaveManager::~SaveManager() {
    Shutdown();
}

bool SaveManager::Initialize() {
    std::cout << "Initializing Save Manager..." << std::endl;

    // Create save directory if it doesn't exist
    std::filesystem::create_directories(m_saveDirectory);

    m_initialized = true;
    std::cout << "Save Manager initialized successfully!" << std::endl;
    return true;
}

void SaveManager::Shutdown() {
    if (m_initialized) {
        std::cout << "Shutting down Save Manager..." << std::endl;
        m_initialized = false;
    }
}

bool SaveManager::SaveGame(int slot, const SaveData& data) {
    if (!m_initialized || slot < 0 || slot >= MAX_SAVE_SLOTS) {
        return false;
    }

    std::string filename = GetSaveFilename(slot);
    return WriteSaveData(filename, data);
}

bool SaveManager::LoadGame(int slot, SaveData& data) {
    if (!m_initialized || slot < 0 || slot >= MAX_SAVE_SLOTS) {
        return false;
    }

    std::string filename = GetSaveFilename(slot);
    return ReadSaveData(filename, data);
}

bool SaveManager::DeleteSave(int slot) {
    if (!m_initialized || slot < 0 || slot >= MAX_SAVE_SLOTS) {
        return false;
    }

    std::string filename = GetSaveFilename(slot);
    return DeleteFile(filename);
}

bool SaveManager::SaveExists(int slot) const {
    if (!m_initialized || slot < 0 || slot >= MAX_SAVE_SLOTS) {
        return false;
    }

    std::string filename = GetSaveFilename(slot);
    return FileExists(filename);
}

std::vector<SaveSlot> SaveManager::GetSaveSlots() const {
    std::vector<SaveSlot> slots(MAX_SAVE_SLOTS);

    for (int i = 0; i < MAX_SAVE_SLOTS; ++i) {
        slots[i] = GetSaveSlot(i);
    }

    return slots;
}

SaveSlot SaveManager::GetSaveSlot(int slot) const {
    SaveSlot saveSlot;
    saveSlot.filename = GetSaveFilename(slot);
    saveSlot.exists = FileExists(saveSlot.filename);

    if (saveSlot.exists) {
        // Note: We can't modify saveSlot.data in a const method
        // This would need to be refactored for full functionality
        saveSlot.timestamp = GetTimestamp();
    }

    return saveSlot;
}

int SaveManager::GetEmptySlot() const {
    for (int i = 1; i < MAX_SAVE_SLOTS; ++i) { // Skip slot 0 (quick save)
        if (!SaveExists(i)) {
            return i;
        }
    }
    return -1; // No empty slots
}

int SaveManager::GetSaveCount() const {
    int count = 0;
    for (int i = 0; i < MAX_SAVE_SLOTS; ++i) {
        if (SaveExists(i)) {
            count++;
        }
    }
    return count;
}

bool SaveManager::QuickSave(const SaveData& data) {
    return SaveGame(QUICK_SAVE_SLOT, data);
}

bool SaveManager::QuickLoad(SaveData& data) {
    return LoadGame(QUICK_SAVE_SLOT, data);
}

bool SaveManager::HasQuickSave() const {
    return SaveExists(QUICK_SAVE_SLOT);
}

bool SaveManager::AutoSave(const SaveData& data) {
    std::string filename = m_saveDirectory + "/autosave.json";
    return WriteSaveData(filename, data);
}

bool SaveManager::LoadAutoSave(SaveData& data) {
    std::string filename = m_saveDirectory + "/autosave.json";
    return ReadSaveData(filename, data);
}

bool SaveManager::HasAutoSave() const {
    std::string filename = m_saveDirectory + "/autosave.json";
    return FileExists(filename);
}

bool SaveManager::SaveSettings(const std::string& settings) {
    std::string filename = GetSettingsFilename();
    std::ofstream file(filename);
    if (file.is_open()) {
        file << settings;
        return true;
    }
    return false;
}

std::string SaveManager::LoadSettings() const {
    std::string filename = GetSettingsFilename();
    std::ifstream file(filename);
    if (file.is_open()) {
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        return content;
    }
    return "";
}

bool SaveManager::SaveToFile0(const SaveData& data) {
    std::string filename = m_saveDirectory + "/file0";
    return WriteSaveData(filename, data);
}

bool SaveManager::LoadFromFile0(SaveData& data) {
    std::string filename = m_saveDirectory + "/file0";
    return ReadSaveData(filename, data);
}

bool SaveManager::SaveToFile9(const SaveData& data) {
    std::string filename = m_saveDirectory + "/file9";
    return WriteSaveData(filename, data);
}

bool SaveManager::LoadFromFile9(SaveData& data) {
    std::string filename = m_saveDirectory + "/file9";
    return ReadSaveData(filename, data);
}

bool SaveManager::DeleteFile0() {
    std::string filename = m_saveDirectory + "/file0";
    return DeleteFile(filename);
}

bool SaveManager::DeleteFile9() {
    std::string filename = m_saveDirectory + "/file9";
    return DeleteFile(filename);
}

bool SaveManager::File0Exists() const {
    std::string filename = m_saveDirectory + "/file0";
    return FileExists(filename);
}

bool SaveManager::File9Exists() const {
    std::string filename = m_saveDirectory + "/file9";
    return FileExists(filename);
}

bool SaveManager::ValidateSave(const SaveData& data) const {
    // Basic validation
    if (data.playerName.empty()) return false;
    if (data.level < 1 || data.level > 20) return false;
    if (data.hp < 0 || data.hp > data.maxHP) return false;
    if (data.maxHP < 1) return false;
    return true;
}

bool SaveManager::VerifySaveIntegrity(int slot) const {
    // TODO: Implement checksum verification
    return SaveExists(slot);
}

bool SaveManager::CreateBackup(int slot) {
    if (!SaveExists(slot)) return false;

    std::string sourceFile = GetSaveFilename(slot);
    std::string backupFile = GetBackupFilename(slot);

    try {
        std::filesystem::copy_file(sourceFile, backupFile,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool SaveManager::RestoreBackup(int slot) {
    if (!HasBackup(slot)) return false;

    std::string sourceFile = GetBackupFilename(slot);
    std::string targetFile = GetSaveFilename(slot);

    try {
        std::filesystem::copy_file(sourceFile, targetFile,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool SaveManager::HasBackup(int slot) const {
    std::string filename = GetBackupFilename(slot);
    return FileExists(filename);
}

std::string SaveManager::GetSaveFilename(int slot) const {
    return m_saveDirectory + "/save" + std::to_string(slot) + ".json";
}

std::string SaveManager::GetBackupFilename(int slot) const {
    return m_saveDirectory + "/save" + std::to_string(slot) + ".bak";
}

std::string SaveManager::GetSettingsFilename() const {
    return m_saveDirectory + "/settings.json";
}

bool SaveManager::WriteSaveData(const std::string& filename, const SaveData& data) {
    if (!ValidateSave(data)) {
        std::cerr << "Invalid save data!" << std::endl;
        return false;
    }

    std::string jsonData = SerializeSaveData(data);

    std::ofstream file(filename);
    if (file.is_open()) {
        file << jsonData;
        std::cout << "Saved game to: " << filename << std::endl;
        return true;
    }

    std::cerr << "Failed to write save file: " << filename << std::endl;
    return false;
}

bool SaveManager::ReadSaveData(const std::string& filename, SaveData& data) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    std::string jsonData((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());

    if (DeserializeSaveData(jsonData, data)) {
        std::cout << "Loaded game from: " << filename << std::endl;
        return true;
    }

    std::cerr << "Failed to parse save file: " << filename << std::endl;
    return false;
}

std::string SaveManager::SerializeSaveData(const SaveData& data) const {
    // Create JSON representation of save data
    std::stringstream json;
    json << "{\n";
    json << "  \"playerName\": \"" << data.playerName << "\",\n";
    json << "  \"level\": " << data.level << ",\n";
    json << "  \"hp\": " << data.hp << ",\n";
    json << "  \"maxHP\": " << data.maxHP << ",\n";
    json << "  \"attack\": " << data.attack << ",\n";
    json << "  \"defense\": " << data.defense << ",\n";
    json << "  \"exp\": " << data.exp << ",\n";
    json << "  \"gold\": " << data.gold << ",\n";
    json << "  \"currentRoom\": \"" << data.currentRoom << "\",\n";
    json << "  \"playerX\": " << data.playerX << ",\n";
    json << "  \"playerY\": " << data.playerY << ",\n";
    json << "  \"weapon\": \"" << data.weapon << "\",\n";
    json << "  \"armor\": \"" << data.armor << "\",\n";
    json << "  \"currentRoute\": " << static_cast<int>(data.currentRoute) << ",\n";
    json << "  \"killCount\": " << data.killCount << ",\n";
    json << "  \"spareCount\": " << data.spareCount << ",\n";
    json << "  \"playtime\": " << data.playtime << ",\n";
    json << "  \"funValue\": " << data.funValue << ",\n";

    // Items array
    json << "  \"items\": [";
    for (size_t i = 0; i < data.items.size(); ++i) {
        json << "\"" << data.items[i] << "\"";
        if (i < data.items.size() - 1) json << ", ";
    }
    json << "],\n";

    // Flags array
    json << "  \"flags\": [";
    for (size_t i = 0; i < data.flags.size(); ++i) {
        json << (data.flags[i] ? "true" : "false");
        if (i < data.flags.size() - 1) json << ", ";
    }
    json << "],\n";

    json << "  \"timestamp\": \"" << GetTimestamp() << "\"\n";
    json << "}";

    return json.str();
}

bool SaveManager::DeserializeSaveData(const std::string& jsonData, SaveData& data) const {
    auto json = JsonParser::Parse(jsonData);
    if (!json || !json->IsObject()) {
        return false;
    }

    // Parse basic data
    if (json->HasKey("playerName")) data.playerName = json->operator[]("playerName")->AsString();
    if (json->HasKey("level")) data.level = json->operator[]("level")->AsInt();
    if (json->HasKey("hp")) data.hp = json->operator[]("hp")->AsInt();
    if (json->HasKey("maxHP")) data.maxHP = json->operator[]("maxHP")->AsInt();
    if (json->HasKey("attack")) data.attack = json->operator[]("attack")->AsInt();
    if (json->HasKey("defense")) data.defense = json->operator[]("defense")->AsInt();
    if (json->HasKey("exp")) data.exp = json->operator[]("exp")->AsInt();
    if (json->HasKey("gold")) data.gold = json->operator[]("gold")->AsInt();
    if (json->HasKey("currentRoom")) data.currentRoom = json->operator[]("currentRoom")->AsString();
    if (json->HasKey("playerX")) data.playerX = json->operator[]("playerX")->AsFloat();
    if (json->HasKey("playerY")) data.playerY = json->operator[]("playerY")->AsFloat();
    if (json->HasKey("weapon")) data.weapon = json->operator[]("weapon")->AsString();
    if (json->HasKey("armor")) data.armor = json->operator[]("armor")->AsString();
    if (json->HasKey("currentRoute")) data.currentRoute = static_cast<RouteType>(json->operator[]("currentRoute")->AsInt());
    if (json->HasKey("killCount")) data.killCount = json->operator[]("killCount")->AsInt();
    if (json->HasKey("spareCount")) data.spareCount = json->operator[]("spareCount")->AsInt();
    if (json->HasKey("playtime")) data.playtime = json->operator[]("playtime")->AsFloat();
    if (json->HasKey("funValue")) data.funValue = json->operator[]("funValue")->AsInt();

    // Parse items array
    if (json->HasKey("items") && json->operator[]("items")->IsArray()) {
        const auto& itemsArray = json->operator[]("items")->AsArray();
        data.items.clear();
        for (const auto& item : itemsArray) {
            data.items.push_back(item->AsString());
        }
    }

    // Parse flags array
    if (json->HasKey("flags") && json->operator[]("flags")->IsArray()) {
        const auto& flagsArray = json->operator[]("flags")->AsArray();
        data.flags.clear();
        data.flags.resize(static_cast<size_t>(GameFlag::COUNT), false);
        for (size_t i = 0; i < flagsArray.size() && i < data.flags.size(); ++i) {
            data.flags[i] = flagsArray[i]->AsBool();
        }
    }

    return true;
}

std::string SaveManager::CalculateChecksum(const SaveData& data) const {
    // Simple checksum implementation
    std::string serialized = SerializeSaveData(data);
    size_t hash = std::hash<std::string>{}(serialized);
    return std::to_string(hash);
}

bool SaveManager::VerifyChecksum(const SaveData& data, const std::string& checksum) const {
    return CalculateChecksum(data) == checksum;
}

std::string SaveManager::GetTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

bool SaveManager::FileExists(const std::string& filename) const {
    std::ifstream file(filename);
    return file.good();
}

bool SaveManager::DeleteFile(const std::string& filename) const {
    try {
        return std::filesystem::remove(filename);
    } catch (const std::exception&) {
        return false;
    }
}
