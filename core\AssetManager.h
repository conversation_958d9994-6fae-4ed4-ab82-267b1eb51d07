#pragma once

#include <unordered_map>
#include <memory>
#include <string>
#include <vector>

// Forward declarations
class Sprite;
class Font;
class AudioClip;
class MusicTrack;

struct DialogueData;
struct MonsterData;
struct ItemData;
struct RoomData;

class AssetManager {
public:
    AssetManager();
    ~AssetManager();
    
    bool Initialize();
    void Shutdown();
    
    // Sprite management
    std::shared_ptr<Sprite> LoadSprite(const std::string& name, const std::string& filename);
    std::shared_ptr<Sprite> GetSprite(const std::string& name) const;
    void UnloadSprite(const std::string& name);
    
    // Font management
    std::shared_ptr<Font> LoadFont(const std::string& name, const std::string& filename, int size = 16);
    std::shared_ptr<Font> GetFont(const std::string& name) const;
    void UnloadFont(const std::string& name);
    
    // Audio management
    std::shared_ptr<AudioClip> LoadSound(const std::string& name, const std::string& filename);
    std::shared_ptr<AudioClip> GetSound(const std::string& name) const;
    std::shared_ptr<MusicTrack> LoadMusic(const std::string& name, const std::string& filename);
    std::shared_ptr<MusicTrack> GetMusic(const std::string& name) const;
    
    // Data management
    bool LoadDialogueData(const std::string& filename);
    bool LoadMonsterData(const std::string& filename);
    bool LoadItemData(const std::string& filename);
    bool LoadRoomData(const std::string& filename);
    
    const DialogueData* GetDialogue(const std::string& key) const;
    const MonsterData* GetMonster(const std::string& name) const;
    const ItemData* GetItem(const std::string& name) const;
    const RoomData* GetRoom(const std::string& name) const;
    
    // Bulk loading for Undertale assets
    bool LoadUndertaleAssets();
    bool LoadCharacterSprites();
    bool LoadUISprites();
    bool LoadBattleSprites();
    bool LoadOverworldSprites();
    bool LoadFonts();
    bool LoadAudio();
    bool LoadGameData();
    
    // Asset validation
    bool ValidateAssets() const;
    void ListLoadedAssets() const;
    
private:
    // Asset containers
    std::unordered_map<std::string, std::shared_ptr<Sprite>> m_sprites;
    std::unordered_map<std::string, std::shared_ptr<Font>> m_fonts;
    std::unordered_map<std::string, std::shared_ptr<AudioClip>> m_sounds;
    std::unordered_map<std::string, std::shared_ptr<MusicTrack>> m_music;
    
    // Game data containers
    std::unordered_map<std::string, std::unique_ptr<DialogueData>> m_dialogues;
    std::unordered_map<std::string, std::unique_ptr<MonsterData>> m_monsters;
    std::unordered_map<std::string, std::unique_ptr<ItemData>> m_items;
    std::unordered_map<std::string, std::unique_ptr<RoomData>> m_rooms;
    
    // Helper functions
    bool FileExists(const std::string& filename) const;
    std::vector<uint8_t> LoadFile(const std::string& filename) const;
    std::string LoadTextFile(const std::string& filename) const;
    
    bool m_initialized;
};
