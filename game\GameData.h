#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>

// Forward declarations
struct Monster;
struct Item;
struct Room;
struct DialogueEntry;

enum class RouteType {
    NEUTRAL,
    PACIFIST,
    GENOCIDE,
    TRUE_PACIFIST
};

enum class GameFlag {
    // Story progression flags
    TORIEL_KILLED,
    PAPYRUS_KILLED,
    UNDYNE_KILLED,
    METTATON_KILLED,
    SANS_KILLED,
    FLOWEY_KILLED,
    
    // Pacifist flags
    TORIEL_SPARED,
    PAPYRUS_SPARED,
    UNDYNE_BEFRIENDED,
    ALPHYS_DATE_COMPLETE,
    TRUE_LAB_COMPLETE,
    
    // Genocide flags
    RUINS_GENOCIDE,
    SNOWDIN_GENOCIDE,
    WATERFALL_GENOCIDE,
    HOTLAND_GENOCIDE,
    
    // Special encounters
    FLOWEY_MET,
    SANS_JUDGEMENT,
    GASTER_ENCOUNTER,
    
    // Fun value events
    GASTER_FOLLOWER_1,
    GASTER_FOLLOWER_2,
    GASTER_FOLLOWER_3,
    <PERSON>Y<PERSON><PERSON><PERSON>_MAN,
    SOUND_TEST,
    REDACTED,
    
    // Misc flags
    NAMED_FALLEN_HUMAN,
    FIRST_SAVE,
    GAME_COMPLETED,
    
    COUNT
};

struct SaveData {
    // Player info
    std::string playerName;
    int level;
    int hp;
    int maxHP;
    int attack;
    int defense;
    int exp;
    int gold;
    
    // Location
    std::string currentRoom;
    float playerX, playerY;
    
    // Inventory
    std::vector<std::string> items;
    std::string weapon;
    std::string armor;
    
    // Game state
    RouteType currentRoute;
    int killCount;
    int spareCount;
    float playtime;
    
    // Flags
    std::vector<bool> flags;
    
    // Fun value
    int funValue;
    
    SaveData();
    void Reset();
};

class GameData {
public:
    GameData();
    ~GameData();
    
    bool Initialize();
    void Shutdown();
    
    // Save data access
    SaveData& GetSaveData() { return m_saveData; }
    const SaveData& GetSaveData() const { return m_saveData; }
    
    // Flag management
    void SetFlag(GameFlag flag, bool value = true);
    bool GetFlag(GameFlag flag) const;
    void ClearAllFlags();
    
    // Route detection
    RouteType DetermineRoute() const;
    bool IsGenocideRoute() const;
    bool IsPacifistRoute() const;
    bool IsTruePacifistRoute() const;
    
    // Fun value system
    void GenerateFunValue();
    int GetFunValue() const { return m_saveData.funValue; }
    void SetFunValue(int value) { m_saveData.funValue = value; }
    
    // Game progression
    void AddKill();
    void AddSpare();
    void GainEXP(int amount);
    void GainGold(int amount);
    void LoseGold(int amount);
    void Heal(int amount);
    void TakeDamage(int amount);
    
    // Inventory management
    bool AddItem(const std::string& itemName);
    bool RemoveItem(const std::string& itemName);
    bool HasItem(const std::string& itemName) const;
    int GetItemCount(const std::string& itemName) const;
    
    // Equipment
    void EquipWeapon(const std::string& weaponName);
    void EquipArmor(const std::string& armorName);
    std::string GetEquippedWeapon() const { return m_saveData.weapon; }
    std::string GetEquippedArmor() const { return m_saveData.armor; }
    
    // Stats calculation
    int GetTotalAttack() const;
    int GetTotalDefense() const;
    
    // Special Undertale mechanics
    bool CanEncounterGaster() const;
    bool ShouldShowSpecialDialogue(const std::string& character) const;
    std::string GetSpecialDialogueKey(const std::string& character) const;
    
private:
    SaveData m_saveData;
    
    // Game constants
    static constexpr int MAX_INVENTORY_SIZE = 8;
    static constexpr int MAX_LEVEL = 20;
    static constexpr int GENOCIDE_KILL_THRESHOLD = 20;
};
